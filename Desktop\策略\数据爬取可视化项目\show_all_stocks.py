#!/usr/bin/env python3
"""
显示爬取结果 - 包含所有相关股票
"""
import json

# 读取结果
with open('topic_stocks.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"=== 东方财富VIP话题榜数据 ===")
print(f"总共获取到 {len(data)} 个话题")
print()

# 统计所有股票
all_stocks = []
stock_count = 0

for item in data:
    print(f"{item['rank']:2d}. {item['topic_title']}")
    print(f"    主要股票: {item['stock_name']} ({item['change_pct']:+.2f}%)")
    
    # 显示所有相关股票
    if 'related_stocks' in item and len(item['related_stocks']) > 0:
        print(f"    相关股票 ({len(item['related_stocks'])}只):")
        for i, stock in enumerate(item['related_stocks']):
            print(f"      {i+1}. {stock['name']} ({stock['change_pct']:+.2f}%)")
            all_stocks.append({
                'topic_rank': item['rank'],
                'topic_title': item['topic_title'],
                'stock_name': stock['name'],
                'change_pct': stock['change_pct']
            })
            stock_count += 1
    
    print(f"    阅读: {item['view_count']} | 讨论: {item['comment_count']}")
    print(f"    内容: {item['topic_content'][:100]}...")
    print()

print(f"=== 统计信息 ===")
print(f"总话题数: {len(data)}")
print(f"总股票数: {stock_count}")
print()

# 按涨跌幅排序显示所有股票
print(f"=== 所有相关股票按涨跌幅排序 ===")
sorted_stocks = sorted(all_stocks, key=lambda x: x['change_pct'], reverse=True)

for i, stock in enumerate(sorted_stocks, 1):
    print(f"{i:2d}. {stock['stock_name']:12s} {stock['change_pct']:+6.2f}% (话题{stock['topic_rank']}: {stock['topic_title'][:30]}...)")

# 保存所有股票数据
with open('all_stocks.json', 'w', encoding='utf-8') as f:
    json.dump(sorted_stocks, f, ensure_ascii=False, indent=2)

print(f"\n所有股票数据已保存到 all_stocks.json")
