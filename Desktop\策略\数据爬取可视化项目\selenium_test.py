#!/usr/bin/env python3
"""
Selenium测试脚本 - 诊断东方财富网站访问问题
"""
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_selenium():
    """测试Selenium是否能正常工作"""
    print("开始Selenium测试...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--disable-logging')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    driver = None
    try:
        print("正在初始化Chrome驱动...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        
        print("Chrome驱动初始化成功")
        
        # 测试访问百度
        print("测试访问百度...")
        driver.get("https://www.baidu.com")
        print(f"百度页面标题: {driver.title}")
        
        # 测试访问目标网站
        url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/topic"
        print(f"测试访问目标网站: {url}")
        driver.get(url)
        print(f"目标网站页面标题: {driver.title}")
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(10)
        
        # 获取页面内容
        page_source = driver.page_source
        print(f"页面源码长度: {len(page_source)}")
        
        # 保存页面源码
        with open('selenium_test_page.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print("页面源码已保存到 selenium_test_page.html")
        
        # 获取页面文本
        body_text = driver.find_element("tag name", "body").text
        print(f"页面文本长度: {len(body_text)}")
        print(f"页面文本前200字符: {body_text[:200]}")
        
        # 保存页面文本
        with open('selenium_test_text.txt', 'w', encoding='utf-8') as f:
            f.write(body_text)
        print("页面文本已保存到 selenium_test_text.txt")
        
        print("Selenium测试完成")
        
    except Exception as e:
        print(f"Selenium测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver:
            driver.quit()
            print("Chrome驱动已关闭")

if __name__ == "__main__":
    test_selenium()
