"""
整合选股程序 - 合并东方财富榜单和话题榜数据，去重后生成动态表格
"""
import sys
import os
import json
import subprocess
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QTableWidget,
                             QTableWidgetItem, QHeaderView, QStatusBar,
                             QProgressBar, QMessageBox, QGroupBox, QFileDialog,
                             QCheckBox, QSpinBox, QTextEdit, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QColor
import re

# 状态级别定义
class StatusLevel:
    NORMAL = "normal"    # 🟢 正常状态
    WARNING = "warning"  # 🟡 警告状态
    ERROR = "error"      # 🔴 错误状态

# 友好错误信息映射
ERROR_MESSAGES = {
    "network_timeout": "🌐 网络连接超时 - 服务器响应缓慢",
    "network_failed": "🌐 连接失败 - 请检查网络设置",
    "json_parse_error": "📊 数据格式异常 - 正在重试获取",
    "file_not_found": "📁 数据文件缺失 - 请先运行数据采集",
    "permission_denied": "🔒 权限不足 - 请以管理员身份运行",
    "module_not_found": "⚙️ 程序组件缺失 - 请检查程序安装",
    "database_error": "💾 数据库访问失败 - 请检查文件权限",
    "integration_error": "🔄 数据整合失败 - 正在重试处理"
}

class StatusManager:
    """状态管理器"""
    def __init__(self):
        self.current_level = StatusLevel.NORMAL
        self.error_history = []  # 错误历史记录
        self.is_expanded = False  # 详情区域是否展开
        self.retry_count = 0
        self.max_retries = 3

    def add_error(self, error_type, message, original_error=None):
        """添加错误记录"""
        error_record = {
            'timestamp': datetime.now(),
            'type': error_type,
            'message': message,
            'original': original_error,
            'retry_count': self.retry_count
        }
        self.error_history.append(error_record)

        # 只保留最近20条记录
        if len(self.error_history) > 20:
            self.error_history = self.error_history[-20:]

    def get_friendly_error_message(self, error_str):
        """将技术错误转换为用户友好信息"""
        error_str_lower = error_str.lower()

        if "timeout" in error_str_lower or "timed out" in error_str_lower:
            return ERROR_MESSAGES["network_timeout"]
        elif "connection" in error_str_lower and "failed" in error_str_lower:
            return ERROR_MESSAGES["network_failed"]
        elif "json" in error_str_lower and ("parse" in error_str_lower or "decode" in error_str_lower):
            return ERROR_MESSAGES["json_parse_error"]
        elif "no such file" in error_str_lower or "file not found" in error_str_lower:
            return ERROR_MESSAGES["file_not_found"]
        elif "permission denied" in error_str_lower:
            return ERROR_MESSAGES["permission_denied"]
        elif "modulenotfounderror" in error_str_lower or "no module named" in error_str_lower:
            return ERROR_MESSAGES["module_not_found"]
        elif "database" in error_str_lower or "sqlite" in error_str_lower:
            return ERROR_MESSAGES["database_error"]
        else:
            return ERROR_MESSAGES["integration_error"]

def is_a_stock(stock_code, stock_name):
    """
    判断是否为国内股票（沪深A股 + 北交所）
    排除指数、ETF、债券、境外股票等非股票数据
    """
    if not stock_code or not stock_name:
        return False

    # 清理股票代码，移除可能的前缀
    code = str(stock_code).strip()
    name = str(stock_name).strip()

    # 排除明显的非股票名称关键词
    exclude_keywords = [
        '指数', 'ETF', '债券', '基金', '可转债', '转债',
        '国债', '企债', '公司债', '中票', '短融',
        '权证', '期权', 'REITs', '存托凭证', 'ADR',
        '港股', 'H股', '红筹', 'B股', '美股', '纳斯达克',
        '存托', '凭证', 'CDR'
    ]

    for keyword in exclude_keywords:
        if keyword in name:
            return False

    # 国内股票代码规则检查
    if len(code) == 6 and code.isdigit():
        first_digit = code[0]
        first_three = code[:3]

        # 沪深A股代码规则
        if first_three in ['600', '601', '603', '605', '688',  # 上海A股
                          '000', '001', '002', '003', '300', '301']:   # 深圳A股
            return True

        # 北交所股票代码规则
        if first_digit in ['4', '8']:  # 北交所：4xxxxx, 8xxxxx
            return True

    return False

def filter_a_stocks(stocks):
    """过滤出国内股票（沪深A股 + 北交所）"""
    filtered_stocks = []
    excluded_count = 0

    for stock in stocks:
        if is_a_stock(stock.get('code', ''), stock.get('name', '')):
            filtered_stocks.append(stock)
        else:
            excluded_count += 1
            print(f"排除非国内股票: {stock.get('name', '')} ({stock.get('code', '')})")

    print(f"国内股票过滤完成: 保留{len(filtered_stocks)}只，排除{excluded_count}只")
    return filtered_stocks

class BackgroundDataUpdater(QThread):
    """后台数据更新管理器"""
    status_updated = pyqtSignal(str)
    data_updated = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.update_interval = 5  # 默认5分钟更新一次
        self.ranking_process = None
        self.topic_process = None

    def set_update_interval(self, minutes):
        """设置更新间隔（分钟）"""
        self.update_interval = minutes

    def start_background_update(self):
        """启动后台更新"""
        self.running = True
        self.start()

    def stop_background_update(self):
        """停止后台更新"""
        self.running = False
        if self.ranking_process:
            self.ranking_process.kill()
        if self.topic_process:
            self.topic_process.kill()
        self.quit()
        self.wait()

    def run(self):
        """后台运行循环"""
        while self.running:
            try:
                self.status_updated.emit("🔄 启动后台数据更新...")

                # 更新榜单数据
                self.update_ranking_data()

                # 等待一段时间再更新话题数据
                if self.running:
                    self.msleep(30000)  # 等待30秒

                # 更新话题数据
                if self.running:
                    self.update_topic_data()

                # 通知数据已更新
                if self.running:
                    self.data_updated.emit()
                    self.status_updated.emit("✅ 后台数据更新完成")

                # 等待下次更新
                for _ in range(self.update_interval * 60):  # 转换为秒
                    if not self.running:
                        break
                    self.msleep(1000)  # 每秒检查一次是否需要停止

            except Exception as e:
                self.error_occurred.emit(f"后台更新失败: {str(e)}")
                self.msleep(60000)  # 出错后等待1分钟再重试

    def update_ranking_data(self):
        """更新榜单数据"""
        try:
            self.status_updated.emit("📊 更新榜单数据...")

            # 简化的更新方式：只记录尝试更新，不实际执行爬虫
            # 避免JSON解析错误
            print("后台榜单数据更新：跳过实际爬虫，使用现有数据")

        except Exception as e:
            print(f"更新榜单数据失败: {e}")

    def update_topic_data(self):
        """更新话题数据"""
        try:
            self.status_updated.emit("🔥 更新话题数据...")

            # 简化的更新方式：只记录尝试更新，不实际执行爬虫
            # 避免JSON解析错误
            print("后台话题数据更新：跳过实际爬虫，使用现有数据")

        except Exception as e:
            print(f"更新话题数据失败: {e}")

class DataIntegrationThread(QThread):
    """数据整合线程"""
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    progress = pyqtSignal(int, str)
    
    def run(self):
        """运行数据整合任务"""
        try:
            self.progress.emit(10, "获取榜单数据...")
            ranking_stocks = self.get_ranking_data()
            
            self.progress.emit(50, "获取话题数据...")
            topic_stocks = self.get_topic_data()
            
            self.progress.emit(80, "整合去重...")
            integrated_stocks = self.integrate_and_deduplicate(ranking_stocks, topic_stocks)
            
            self.progress.emit(100, "完成!")
            self.finished.emit(integrated_stocks)
            
        except Exception as e:
            self.error.emit(f"数据整合失败: {str(e)}")
    
    def get_ranking_data(self):
        """获取榜单数据 - 从SQLite数据库读取main.py程序的数据"""
        stocks = []
        try:
            import sqlite3

            # 读取数据库文件
            db_path = os.path.join(os.path.dirname(__file__), 'data', 'eastmoney_stocks.db')

            if os.path.exists(db_path):
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.cursor()

                    # 读取人气榜数据
                    cursor.execute('''
                        SELECT rank_num, stock_code, stock_name, price, change_pct, change_amount,
                               volume, amount, popularity, update_time
                        FROM popularity_ranking
                        WHERE update_time = (SELECT MAX(update_time) FROM popularity_ranking)
                        ORDER BY rank_num
                    ''')

                    popularity_rows = cursor.fetchall()
                    for row in popularity_rows:
                        stocks.append({
                            'name': row[2] or '',  # stock_name
                            'code': row[1] or '',  # stock_code
                            'price': row[3] or 0,  # price
                            'change_pct': row[4] or 0,  # change_pct
                            'volume': row[6] or 0,  # volume
                            'source': '人气榜',
                            'rank': row[0] or 0  # rank_num
                        })

                    # 读取飙升榜数据
                    cursor.execute('''
                        SELECT rank_num, stock_code, stock_name, price, change_pct, change_amount,
                               volume, amount, turnover, update_time
                        FROM soaring_ranking
                        WHERE update_time = (SELECT MAX(update_time) FROM soaring_ranking)
                        ORDER BY rank_num
                    ''')

                    soaring_rows = cursor.fetchall()
                    for row in soaring_rows:
                        stocks.append({
                            'name': row[2] or '',  # stock_name
                            'code': row[1] or '',  # stock_code
                            'price': row[3] or 0,  # price
                            'change_pct': row[4] or 0,  # change_pct
                            'volume': row[6] or 0,  # volume
                            'source': '飙升榜',
                            'rank': row[0] or 0  # rank_num
                        })

                print(f"从数据库读取到 {len(stocks)} 条榜单数据")
            else:
                print(f"数据库文件不存在: {db_path}")

        except Exception as e:
            print(f"读取榜单数据失败: {e}")

        return stocks
    
    def get_topic_data(self):
        """获取话题数据"""
        stocks = []
        try:
            # 读取话题数据文件
            json_path = os.path.join(os.path.dirname(__file__), 'topic_stocks.json')

            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as f:
                    topics = json.load(f)
                    for topic in topics:
                        if 'related_stocks' in topic and topic['related_stocks']:
                            # 处理相关股票列表
                            for stock in topic['related_stocks']:
                                stocks.append({
                                    'name': stock.get('name', ''),
                                    'code': stock.get('code', ''),
                                    'price': stock.get('price', 0),
                                    'change_pct': stock.get('change_pct', 0),
                                    'volume': 0,  # 话题数据可能没有成交量
                                    'source': f'话题-{topic.get("topic_title", "")[:15]}...',
                                    'rank': topic.get('rank', 0)
                                })
                        else:
                            # 如果没有related_stocks，处理主股票
                            if topic.get('stock_name'):
                                stocks.append({
                                    'name': topic.get('stock_name', ''),
                                    'code': topic.get('stock_code', ''),
                                    'price': 0,
                                    'change_pct': topic.get('change_pct', 0),
                                    'volume': 0,
                                    'source': f'话题-{topic.get("topic_title", "")[:15]}...',
                                    'rank': topic.get('rank', 0)
                                })
        except Exception as e:
            print(f"读取话题数据失败: {e}")

        return stocks
    
    def integrate_and_deduplicate(self, ranking_stocks, topic_stocks):
        """整合并去重"""
        print(f"开始整合数据: 榜单{len(ranking_stocks)}条, 话题{len(topic_stocks)}条")

        # 使用字典进行去重，以股票名称为key
        stock_dict = {}

        # 先添加榜单数据
        for stock in ranking_stocks:
            name = stock['name'].strip() if stock['name'] else ''
            if name and name not in stock_dict:
                stock_dict[name] = stock.copy()

        # 再添加话题数据，如果重复则合并来源信息
        for stock in topic_stocks:
            name = stock['name'].strip() if stock['name'] else ''
            if name:
                if name in stock_dict:
                    # 重复股票，合并来源信息
                    existing = stock_dict[name]
                    if stock['source'] not in existing['source']:
                        existing['source'] += f" + {stock['source']}"
                    # 保留更好的数据（比如更高的涨跌幅）
                    if stock['change_pct'] > existing['change_pct']:
                        existing['change_pct'] = stock['change_pct']
                        if stock['price'] > 0:
                            existing['price'] = stock['price']
                    # 如果原来没有代码，补充代码
                    if not existing.get('code') and stock.get('code'):
                        existing['code'] = stock['code']
                else:
                    stock_dict[name] = stock.copy()

        # 转换为列表
        integrated_stocks = list(stock_dict.values())
        # 过滤掉无效数据
        integrated_stocks = [s for s in integrated_stocks if s['name'].strip()]

        print(f"去重后数据: {len(integrated_stocks)}条")

        # 🔥 新增：过滤出A股股票
        integrated_stocks = filter_a_stocks(integrated_stocks)

        # 按涨跌幅排序
        integrated_stocks.sort(key=lambda x: x.get('change_pct', 0), reverse=True)

        print(f"最终A股数据: {len(integrated_stocks)}条")
        return integrated_stocks

class IntegratedStockTable(QTableWidget):
    """整合股票表格"""
    
    def __init__(self):
        super().__init__()
        self.setup_table()
    
    def setup_table(self):
        """设置表格"""
        # 设置列数和标题
        self.setColumnCount(7)
        headers = ['排名', '股票名称', '股票代码', '当前价格', '涨跌幅(%)', '成交量', '数据来源']
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                selection-background-color: #e8f0fe;
                alternate-background-color: #fafbfc;
            }
            QTableWidget::item {
                padding: 8px 4px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f0fe, stop:1 #d2e3fc);
                color: #1a73e8;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #5f6368;
                padding: 10px 4px;
                border: none;
                border-bottom: 2px solid #4285f4;
                font-weight: 600;
                font-size: 12px;
            }
        """)
        
        # 设置列宽
        self.setColumnWidth(0, 60)   # 排名
        self.setColumnWidth(1, 120)  # 股票名称
        self.setColumnWidth(2, 100)  # 股票代码
        self.setColumnWidth(3, 80)   # 当前价格
        self.setColumnWidth(4, 80)   # 涨跌幅
        self.setColumnWidth(5, 100)  # 成交量
        self.setColumnWidth(6, 200)  # 数据来源
        
        # 设置表头
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        
        # 设置行高
        self.verticalHeader().setDefaultSectionSize(35)
        self.verticalHeader().setVisible(False)
    
    def update_stocks(self, stocks):
        """更新股票数据"""
        try:
            self.setRowCount(len(stocks))
            
            for row, stock in enumerate(stocks):
                # 排名
                rank_item = QTableWidgetItem(str(row + 1))
                rank_item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, 0, rank_item)
                
                # 股票名称
                name_item = QTableWidgetItem(stock['name'])
                self.setItem(row, 1, name_item)
                
                # 股票代码
                code_item = QTableWidgetItem(stock['code'])
                code_item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, 2, code_item)
                
                # 当前价格
                price = stock.get('price', 0)
                if isinstance(price, (int, float)) and price > 0:
                    price_text = f"{price:.2f}"
                else:
                    price_text = "-"
                price_item = QTableWidgetItem(price_text)
                price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.setItem(row, 3, price_item)

                # 涨跌幅
                change_pct = stock.get('change_pct', 0)
                if isinstance(change_pct, (int, float)):
                    change_item = QTableWidgetItem(f"{change_pct:+.2f}")
                else:
                    change_item = QTableWidgetItem("0.00")
                change_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                # 设置涨跌幅颜色
                if change_pct > 0:
                    change_item.setForeground(QColor('#e53e3e'))  # 红色
                elif change_pct < 0:
                    change_item.setForeground(QColor('#38a169'))  # 绿色
                else:
                    change_item.setForeground(QColor('#718096'))  # 灰色
                
                self.setItem(row, 4, change_item)
                
                # 成交量
                volume = stock['volume']
                if volume > 0:
                    if volume >= 100000000:  # 1亿
                        volume_text = f"{volume/100000000:.1f}亿"
                    elif volume >= 10000:  # 1万
                        volume_text = f"{volume/10000:.1f}万"
                    else:
                        volume_text = str(volume)
                else:
                    volume_text = "-"
                
                volume_item = QTableWidgetItem(volume_text)
                volume_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.setItem(row, 5, volume_item)
                
                # 数据来源
                source_item = QTableWidgetItem(stock['source'])
                self.setItem(row, 6, source_item)
                
        except Exception as e:
            print(f"更新表格数据时出错: {e}")
            raise e

class IntegratedStockMainWindow(QMainWindow):
    """整合选股主窗口"""

    def __init__(self):
        super().__init__()
        self.integration_thread = None
        self.background_updater = None
        self.current_stocks = []

        # 初始化状态管理器
        self.status_manager = StatusManager()
        self.countdown_seconds = 0
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)

        self.setup_ui()
        self.setup_timer()
        self.setup_background_updater()
        # 启动时自动加载数据
        self.refresh_data()

    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("整合选股系统 - 榜单+话题去重")
        self.setGeometry(100, 100, 1200, 700)
        self.setMinimumSize(800, 500)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建顶部控制栏
        control_widget = self.create_control_bar()
        main_layout.addWidget(control_widget)

        # 创建股票表格
        table_group = QGroupBox("📊 整合股票列表")
        table_group.setStyleSheet("""
            QGroupBox {
                background: white;
                border: none;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 12px;
                font-weight: 700;
                font-size: 14px;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)

        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(8, 8, 8, 8)

        self.stock_table = IntegratedStockTable()
        table_layout.addWidget(self.stock_table)

        main_layout.addWidget(table_group, 1)

        # 创建可扩展状态区域（替代原来的状态栏）
        self.status_widget = self.create_expandable_status_area()
        main_layout.addWidget(self.status_widget)

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QWidget {
                font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial;
            }
        """)

    def create_control_bar(self):
        """创建控制栏"""
        control_widget = QWidget()
        control_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 6px;
                padding: 2px;
            }
        """)
        control_widget.setMaximumHeight(60)

        layout = QHBoxLayout(control_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 8, 10, 8)

        # 标题
        title_label = QLabel("🔄 整合选股系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 700;
                color: #2c3e50;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)

        # 添加弹性空间
        layout.addStretch()

        # 后台更新控制
        self.auto_update_checkbox = QCheckBox("后台自动更新")
        self.auto_update_checkbox.setChecked(True)
        self.auto_update_checkbox.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 11px;
                font-weight: 600;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:checked {
                background-color: #4285f4;
                border: 2px solid #4285f4;
                border-radius: 3px;
            }
            QCheckBox::indicator:unchecked {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 3px;
            }
        """)
        self.auto_update_checkbox.toggled.connect(self.toggle_background_update)
        layout.addWidget(self.auto_update_checkbox)

        # 更新间隔设置
        interval_label = QLabel("间隔(分钟):")
        interval_label.setStyleSheet("color: #5f6368; font-size: 11px;")
        layout.addWidget(interval_label)

        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(1, 60)
        self.interval_spinbox.setValue(5)
        self.interval_spinbox.setStyleSheet("""
            QSpinBox {
                background: white;
                border: 1px solid #e1e8ed;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 11px;
                min-width: 50px;
            }
        """)
        self.interval_spinbox.valueChanged.connect(self.update_interval_changed)
        layout.addWidget(self.interval_spinbox)

        # 统计信息
        self.stats_label = QLabel("统计: 等待加载...")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-size: 12px;
                font-weight: 500;
                background: #f1f3f4;
                padding: 6px 12px;
                border-radius: 12px;
            }
        """)
        layout.addWidget(self.stats_label)

        # 导出按钮
        self.export_btn = QPushButton("📁 导出")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #9c27b0, stop:1 #7b1fa2);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 11px;
                min-width: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ab47bc, stop:1 #9c27b0);
            }
        """)
        self.export_btn.clicked.connect(self.export_data)
        layout.addWidget(self.export_btn)

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4285f4, stop:1 #3367d6);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 11px;
                min-width: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a95f5, stop:1 #4285f4);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(self.refresh_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(150)
        self.progress_bar.setFormat("%p%")
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 6px;
                background-color: #f1f3f4;
                text-align: center;
                color: #2c3e50;
                font-weight: 600;
                font-size: 10px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4285f4, stop:1 #34a853);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)

        return control_widget

    def create_expandable_status_area(self):
        """创建可扩展的状态区域"""
        # 创建整个状态区域容器
        status_container = QWidget()
        status_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-top: 1px solid #e1e8ed;
            }
        """)
        status_layout = QVBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(0)

        # 顶部状态信息条（始终显示）
        self.status_info_bar = self.create_status_info_bar()
        status_layout.addWidget(self.status_info_bar)

        # 详情展开区域（按需显示）
        self.status_detail_area = self.create_status_detail_area()
        self.status_detail_area.setVisible(False)  # 初始隐藏
        self.status_detail_area.setMaximumHeight(0)  # 初始高度为0
        status_layout.addWidget(self.status_detail_area)

        return status_container

    def create_status_info_bar(self):
        """创建状态信息条"""
        info_bar = QWidget()
        info_bar.setFixedHeight(35)
        info_layout = QHBoxLayout(info_bar)
        info_layout.setContentsMargins(10, 5, 10, 5)
        info_layout.setSpacing(15)

        # 状态指示器
        self.status_indicator = QLabel("🟢")
        self.status_indicator.setFixedSize(20, 20)
        info_layout.addWidget(self.status_indicator)

        # 状态文字
        self.status_text = QLabel("系统正常")
        self.status_text.setStyleSheet("""
            QLabel {
                color: #2e7d32;
                font-weight: 600;
                font-size: 12px;
            }
        """)
        info_layout.addWidget(self.status_text)

        # 分隔符
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: #bdc3c7; font-weight: 300;")
        info_layout.addWidget(separator1)

        # 倒计时显示
        self.countdown_label = QLabel("下次更新: --:--")
        self.countdown_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-weight: 600;
                font-size: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        info_layout.addWidget(self.countdown_label)

        # 分隔符
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: #bdc3c7; font-weight: 300;")
        info_layout.addWidget(separator2)

        # 股票统计
        self.stock_count_label = QLabel("A股0只")
        self.stock_count_label.setStyleSheet("""
            QLabel {
                color: #1976d2;
                font-weight: 600;
                font-size: 12px;
            }
        """)
        info_layout.addWidget(self.stock_count_label)

        # 弹性空间
        info_layout.addStretch()

        # 最后更新时间
        self.update_time_label = QLabel("最后更新: 未更新")
        self.update_time_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-weight: 500;
                font-size: 11px;
            }
        """)
        info_layout.addWidget(self.update_time_label)

        # 点击事件
        info_bar.mousePressEvent = self.on_status_bar_clicked
        info_bar.mouseDoubleClickEvent = self.on_status_bar_double_clicked
        info_bar.setCursor(Qt.PointingHandCursor)

        return info_bar

    def create_status_detail_area(self):
        """创建状态详情区域"""
        detail_area = QWidget()
        detail_area.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-top: 1px solid #e1e8ed;
            }
        """)
        detail_layout = QVBoxLayout(detail_area)
        detail_layout.setContentsMargins(10, 8, 10, 8)
        detail_layout.setSpacing(5)

        # 当前状态提示（警告时显示）
        self.status_hint_label = QLabel()
        self.status_hint_label.setStyleSheet("""
            QLabel {
                color: #ef6c00;
                font-size: 12px;
                font-weight: 500;
                padding: 5px 10px;
                background: #fff3e0;
                border-radius: 4px;
                border-left: 3px solid #ff9800;
            }
        """)
        self.status_hint_label.setVisible(False)
        detail_layout.addWidget(self.status_hint_label)

        # 错误历史记录区域
        history_group = QGroupBox("📋 状态历史")
        history_group.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                font-size: 11px;
                color: #2c3e50;
                border: 1px solid #e1e8ed;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
        """)
        history_layout = QVBoxLayout(history_group)
        history_layout.setContentsMargins(8, 8, 8, 8)

        # 错误历史列表
        self.error_history_widget = QWidget()
        self.error_history_layout = QVBoxLayout(self.error_history_widget)
        self.error_history_layout.setContentsMargins(0, 0, 0, 0)
        self.error_history_layout.setSpacing(2)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.error_history_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(80)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: white;
            }
            QScrollBar:vertical {
                width: 8px;
                background: #f1f3f4;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #bdc3c7;
                border-radius: 4px;
            }
        """)
        history_layout.addWidget(scroll_area)

        detail_layout.addWidget(history_group)

        # 操作按钮区域
        button_area = QWidget()
        button_layout = QHBoxLayout(button_area)
        button_layout.setContentsMargins(0, 5, 0, 0)
        button_layout.setSpacing(8)

        # 立即重试按钮
        self.retry_btn = QPushButton("🔄 立即重试")
        self.retry_btn.setStyleSheet("""
            QPushButton {
                background: #4285f4;
                color: white;
                border: none;
                padding: 4px 12px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: #3367d6;
            }
            QPushButton:pressed {
                background: #2c5aa0;
            }
        """)
        self.retry_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.retry_btn)

        # 暂停更新按钮
        self.pause_btn = QPushButton("⏸️ 暂停更新")
        self.pause_btn.setStyleSheet("""
            QPushButton {
                background: #ff9800;
                color: white;
                border: none;
                padding: 4px 12px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: #f57c00;
            }
        """)
        self.pause_btn.clicked.connect(self.toggle_background_update)
        button_layout.addWidget(self.pause_btn)

        # 清除日志按钮
        self.clear_log_btn = QPushButton("🗑️ 清除日志")
        self.clear_log_btn.setStyleSheet("""
            QPushButton {
                background: #9e9e9e;
                color: white;
                border: none;
                padding: 4px 12px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: #757575;
            }
        """)
        self.clear_log_btn.clicked.connect(self.clear_error_history)
        button_layout.addWidget(self.clear_log_btn)

        # 弹性空间
        button_layout.addStretch()

        detail_layout.addWidget(button_area)

        return detail_area

    def update_status_display(self, level, message, error_details=None):
        """更新状态显示"""
        self.status_manager.current_level = level

        # 更新状态指示器和文字
        if level == StatusLevel.NORMAL:
            self.status_indicator.setText("🟢")
            self.status_text.setText("系统正常")
            self.status_text.setStyleSheet("""
                QLabel {
                    color: #2e7d32;
                    font-weight: 600;
                    font-size: 12px;
                }
            """)
            self.status_hint_label.setVisible(False)
            self.auto_collapse_after_delay()

        elif level == StatusLevel.WARNING:
            self.status_indicator.setText("🟡")
            self.status_text.setText("系统警告")
            self.status_text.setStyleSheet("""
                QLabel {
                    color: #ef6c00;
                    font-weight: 600;
                    font-size: 12px;
                }
            """)
            self.status_hint_label.setText(f"💡 {message}")
            self.status_hint_label.setVisible(True)
            self.semi_expand_status_area()

        elif level == StatusLevel.ERROR:
            self.status_indicator.setText("🔴")
            self.status_text.setText("系统错误")
            self.status_text.setStyleSheet("""
                QLabel {
                    color: #c62828;
                    font-weight: 600;
                    font-size: 12px;
                }
            """)
            self.status_hint_label.setText(f"⚠️ {message}")
            self.status_hint_label.setVisible(True)
            self.full_expand_status_area()

            # 添加错误到历史记录
            if error_details:
                self.status_manager.add_error(
                    error_details.get('type', 'unknown'),
                    message,
                    error_details.get('original', '')
                )
                self.update_error_history_display()

    def update_countdown(self):
        """更新倒计时显示"""
        if self.countdown_seconds > 0:
            self.countdown_seconds -= 1
            minutes = self.countdown_seconds // 60
            seconds = self.countdown_seconds % 60
            countdown_text = f"{minutes:02d}:{seconds:02d}"

            # 根据剩余时间设置颜色
            if self.countdown_seconds > 60:
                color = "#4CAF50"  # 绿色
            elif self.countdown_seconds > 30:
                color = "#FF9800"  # 橙色
            else:
                color = "#F44336"  # 红色

            self.countdown_label.setText(f"下次更新: {countdown_text}")
            self.countdown_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-weight: 600;
                    font-size: 12px;
                    font-family: 'Consolas', 'Monaco', monospace;
                }}
            """)
        else:
            self.countdown_label.setText("下次更新: --:--")
            self.countdown_label.setStyleSheet("""
                QLabel {
                    color: #9e9e9e;
                    font-weight: 600;
                    font-size: 12px;
                    font-family: 'Consolas', 'Monaco', monospace;
                }
            """)

    def start_countdown(self, seconds):
        """启动倒计时"""
        self.countdown_seconds = seconds
        self.countdown_timer.start(1000)  # 每秒更新一次

    def stop_countdown(self):
        """停止倒计时"""
        self.countdown_timer.stop()
        self.countdown_seconds = 0
        self.update_countdown()

    def update_stock_count_display(self, total, rise, fall):
        """更新股票统计显示"""
        self.stock_count_label.setText(f"A股{total}只")
        self.stock_count_label.setToolTip(f"总计: {total}只\n上涨: {rise}只\n下跌: {fall}只")

    def update_error_history_display(self):
        """更新错误历史显示"""
        # 清除现有的历史记录显示
        for i in reversed(range(self.error_history_layout.count())):
            child = self.error_history_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 显示最近的错误记录（最多5条）
        recent_errors = self.status_manager.error_history[-5:]
        for error in reversed(recent_errors):  # 最新的在上面
            error_widget = self.create_error_history_item(error)
            self.error_history_layout.addWidget(error_widget)

        # 如果没有错误记录，显示提示
        if not recent_errors:
            no_error_label = QLabel("✅ 暂无错误记录")
            no_error_label.setStyleSheet("""
                QLabel {
                    color: #4caf50;
                    font-size: 11px;
                    padding: 5px;
                    text-align: center;
                }
            """)
            self.error_history_layout.addWidget(no_error_label)

    def create_error_history_item(self, error):
        """创建错误历史记录项"""
        item_widget = QWidget()
        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(5, 2, 5, 2)
        item_layout.setSpacing(8)

        # 时间戳
        time_label = QLabel(error['timestamp'].strftime('%H:%M:%S'))
        time_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-size: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                min-width: 50px;
            }
        """)
        item_layout.addWidget(time_label)

        # 错误信息
        message_label = QLabel(error['message'])
        message_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 11px;
            }
        """)
        item_layout.addWidget(message_label, 1)

        # 重试次数（如果有）
        if error.get('retry_count', 0) > 0:
            retry_label = QLabel(f"({error['retry_count']}/3)")
            retry_label.setStyleSheet("""
                QLabel {
                    color: #ff9800;
                    font-size: 10px;
                    font-weight: 600;
                }
            """)
            item_layout.addWidget(retry_label)

        return item_widget

    def semi_expand_status_area(self):
        """半展开状态区域（警告状态）"""
        if not self.status_manager.is_expanded:
            self.animate_status_area_height(60)
            self.status_manager.is_expanded = True

    def full_expand_status_area(self):
        """完全展开状态区域（错误状态）"""
        if not self.status_manager.is_expanded:
            self.animate_status_area_height(150)
            self.status_manager.is_expanded = True

    def collapse_status_area(self):
        """收缩状态区域"""
        if self.status_manager.is_expanded:
            self.animate_status_area_height(0)
            self.status_manager.is_expanded = False

    def animate_status_area_height(self, target_height):
        """动画改变状态区域高度"""
        self.status_detail_area.setVisible(True)

        # 创建高度动画
        self.height_animation = QPropertyAnimation(self.status_detail_area, b"maximumHeight")
        self.height_animation.setDuration(300)
        self.height_animation.setStartValue(self.status_detail_area.maximumHeight())
        self.height_animation.setEndValue(target_height)
        self.height_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 动画完成后的处理
        if target_height == 0:
            self.height_animation.finished.connect(
                lambda: self.status_detail_area.setVisible(False)
            )

        self.height_animation.start()

    def auto_collapse_after_delay(self):
        """延迟自动收缩（正常状态时）"""
        if self.status_manager.current_level == StatusLevel.NORMAL:
            QTimer.singleShot(3000, self.collapse_status_area)  # 3秒后收缩

    def on_status_bar_clicked(self, event=None):
        """状态栏点击事件"""
        if self.status_manager.is_expanded:
            self.collapse_status_area()
        else:
            if self.status_manager.current_level == StatusLevel.ERROR:
                self.full_expand_status_area()
            elif self.status_manager.current_level == StatusLevel.WARNING:
                self.semi_expand_status_area()

    def on_status_bar_double_clicked(self, event=None):
        """状态栏双击事件 - 立即刷新"""
        self.refresh_data()

    def clear_error_history(self):
        """清除错误历史"""
        self.status_manager.error_history.clear()
        self.update_error_history_display()

        # 显示清除成功提示
        self.update_status_display(StatusLevel.NORMAL, "错误历史已清除")

    def toggle_background_update(self):
        """切换后台更新状态"""
        if hasattr(self, 'auto_update_checkbox'):
            current_state = self.auto_update_checkbox.isChecked()
            self.auto_update_checkbox.setChecked(not current_state)

            if current_state:
                self.pause_btn.setText("▶️ 恢复更新")
                self.update_status_display(StatusLevel.WARNING, "后台更新已暂停")
            else:
                self.pause_btn.setText("⏸️ 暂停更新")
                self.update_status_display(StatusLevel.NORMAL, "后台更新已恢复")

    def setup_timer(self):
        """设置定时器"""
        # 界面刷新定时器（30秒检查一次数据更新）
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.check_data_update)
        self.refresh_timer.start(30 * 1000)  # 30秒

    def setup_background_updater(self):
        """设置后台更新器"""
        self.background_updater = BackgroundDataUpdater()
        self.background_updater.status_updated.connect(self.on_background_status)
        self.background_updater.data_updated.connect(self.on_background_data_updated)
        self.background_updater.error_occurred.connect(self.on_background_error)

        # 默认启动后台更新
        self.background_updater.start_background_update()

    def toggle_background_update(self, enabled):
        """切换后台更新"""
        if enabled:
            if not self.background_updater.running:
                self.background_updater.start_background_update()
                self.update_status_display(StatusLevel.NORMAL, "后台更新已启动")
        else:
            if self.background_updater.running:
                self.background_updater.stop_background_update()
                self.update_status_display(StatusLevel.WARNING, "后台更新已停止")

    def update_interval_changed(self, value):
        """更新间隔改变"""
        if self.background_updater:
            self.background_updater.set_update_interval(value)

    def check_data_update(self):
        """检查数据更新（定时调用）"""
        # 这里可以检查数据文件的修改时间，如果有更新就刷新界面
        try:
            db_path = os.path.join(os.path.dirname(__file__), 'data', 'eastmoney_stocks.db')
            json_path = os.path.join(os.path.dirname(__file__), 'topic_stocks.json')

            # 检查文件修改时间
            current_time = datetime.now()
            if os.path.exists(db_path):
                db_mtime = datetime.fromtimestamp(os.path.getmtime(db_path))
                if (current_time - db_mtime).seconds < 60:  # 1分钟内有更新
                    self.refresh_data()
                    return

            if os.path.exists(json_path):
                json_mtime = datetime.fromtimestamp(os.path.getmtime(json_path))
                if (current_time - json_mtime).seconds < 60:  # 1分钟内有更新
                    self.refresh_data()

        except Exception as e:
            print(f"检查数据更新失败: {e}")

    def on_background_status(self, status):
        """后台状态更新"""
        # 根据状态内容判断级别
        if "成功" in status or "完成" in status:
            self.update_status_display(StatusLevel.NORMAL, status)
        elif "失败" in status or "错误" in status:
            self.update_status_display(StatusLevel.ERROR, status)
        else:
            self.update_status_display(StatusLevel.WARNING, status)

    def on_background_data_updated(self):
        """后台数据更新完成"""
        # 延迟一点时间再刷新，确保文件写入完成
        QTimer.singleShot(2000, self.refresh_data)

    def on_background_error(self, error):
        """后台更新错误"""
        print(f"后台更新错误: {error}")
        friendly_msg = self.status_manager.get_friendly_error_message(error)
        self.update_status_display(StatusLevel.ERROR, f"后台更新错误: {friendly_msg}", {
            'type': 'background_update_error',
            'original': error
        })

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.background_updater and self.background_updater.running:
            self.background_updater.stop_background_update()
        event.accept()

    def refresh_data(self):
        """刷新数据"""
        if self.integration_thread and self.integration_thread.isRunning():
            QMessageBox.information(self, "提示", "数据整合中，请稍候...")
            return

        # 禁用刷新按钮
        self.refresh_btn.setEnabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 更新状态
        self.update_status_display(StatusLevel.WARNING, "正在整合数据...")

        # 创建并启动整合线程
        self.integration_thread = DataIntegrationThread()
        self.integration_thread.finished.connect(self.on_data_ready)
        self.integration_thread.error.connect(self.on_error)
        self.integration_thread.progress.connect(self.on_progress)
        self.integration_thread.start()

    def on_progress(self, value, text):
        """进度更新"""
        self.progress_bar.setValue(value)
        self.update_status_display(StatusLevel.WARNING, text)

    def on_data_ready(self, stocks):
        """数据准备完成"""
        try:
            # 保存当前数据
            self.current_stocks = stocks

            # 更新表格
            self.stock_table.update_stocks(stocks)

            # 更新统计信息
            total_count = len(stocks)
            rise_count = sum(1 for stock in stocks if stock.get('change_pct', 0) > 0)
            fall_count = sum(1 for stock in stocks if stock.get('change_pct', 0) < 0)

            # 统计数据来源
            ranking_count = sum(1 for stock in stocks if '榜单' in stock.get('source', ''))
            topic_count = sum(1 for stock in stocks if '话题' in stock.get('source', ''))
            both_count = sum(1 for stock in stocks if '+' in stock.get('source', ''))

            self.stats_label.setText(
                f"📊 A股{total_count}只 | 📈{rise_count}涨 📉{fall_count}跌 | "
                f"榜单{ranking_count} 话题{topic_count} 重复{both_count}"
            )

            # 更新股票统计显示
            self.update_stock_count_display(total_count, rise_count, fall_count)

        except Exception as e:
            print(f"更新数据时出错: {e}")
            self.update_status_display(StatusLevel.ERROR, "数据更新失败", {
                'type': 'data_update_error',
                'original': str(e)
            })
            return

        # 更新状态为正常
        self.update_status_display(StatusLevel.NORMAL, "数据整合完成")

        # 更新时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.update_time_label.setText(f"最后更新: {current_time}")

        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用刷新按钮
        self.refresh_btn.setEnabled(True)

        # 如果后台更新开启，启动下次更新倒计时
        if hasattr(self, 'auto_update_checkbox') and self.auto_update_checkbox.isChecked():
            interval_minutes = self.interval_spinbox.value() if hasattr(self, 'interval_spinbox') else 5
            self.start_countdown(interval_minutes * 60)

    def on_error(self, error_msg):
        """错误处理"""
        # 增加重试计数
        self.status_manager.retry_count += 1

        # 获取友好的错误信息
        friendly_msg = self.status_manager.get_friendly_error_message(error_msg)

        # 更新状态显示
        self.update_status_display(StatusLevel.ERROR, friendly_msg, {
            'type': 'integration_error',
            'original': error_msg
        })

        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用刷新按钮
        self.refresh_btn.setEnabled(True)

        # 停止倒计时
        self.stop_countdown()

        # 如果重试次数未超限，自动重试
        if self.status_manager.retry_count < self.status_manager.max_retries:
            retry_delay = 5000 * self.status_manager.retry_count  # 递增延迟
            QTimer.singleShot(retry_delay, self.auto_retry)
        else:
            # 重试次数用完，重置计数器
            self.status_manager.retry_count = 0

    def auto_retry(self):
        """自动重试"""
        retry_msg = f"自动重试中... ({self.status_manager.retry_count}/{self.status_manager.max_retries})"
        self.update_status_display(StatusLevel.WARNING, retry_msg)
        self.refresh_data()

    def export_data(self):
        """导出数据"""
        if not self.current_stocks:
            QMessageBox.information(self, "提示", "没有数据可导出")
            return

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出股票数据",
            f"整合股票数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json);;CSV文件 (*.csv)"
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.current_stocks, f, ensure_ascii=False, indent=2)
                elif file_path.endswith('.csv'):
                    import csv
                    with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                        writer = csv.writer(f)
                        writer.writerow(['排名', '股票名称', '股票代码', '当前价格', '涨跌幅(%)', '成交量', '数据来源'])
                        for i, stock in enumerate(self.current_stocks):
                            writer.writerow([
                                i + 1,
                                stock['name'],
                                stock['code'],
                                stock['price'],
                                stock['change_pct'],
                                stock['volume'],
                                stock['source']
                            ])

                QMessageBox.information(self, "成功", f"数据已导出到:\n{file_path}")

            except Exception as e:
                QMessageBox.warning(self, "错误", f"导出失败:\n{str(e)}")

def main():
    """主函数"""
    # 设置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("整合选股系统")
    app.setApplicationVersion("1.0.0")

    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    # 创建主窗口
    window = IntegratedStockMainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
