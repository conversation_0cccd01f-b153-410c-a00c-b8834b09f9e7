"""
基于网页的东方财富数据爬虫
"""
import requests
import json
import time
import logging
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import re

logger = logging.getLogger(__name__)

class EastMoneyWebScraper:
    """东方财富网页爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        self.driver = None
    
    def init_selenium(self):
        """初始化Selenium WebDriver"""
        if self.driver is not None:
            return True
            
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Selenium WebDriver 初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Selenium WebDriver 初始化失败: {e}")
            return False
    
    def fetch_hotstock_from_web(self):
        """从网页爬取热股榜数据"""
        url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock"
        
        # 首先尝试API方式（更可靠）
        api_data = self._fetch_from_api()
        if api_data:
            logger.info("使用API方式获取数据成功")
            return api_data
        
        # 如果API失败，尝试网页爬取
        logger.info("API方式失败，尝试网页爬取")
        return self._fetch_from_selenium(url)
    
    def _fetch_from_api(self):
        """使用API方式获取数据（推荐）"""
        try:
            # 使用人气排行榜API，因为它包含目标股票
            api_url = "https://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'cb': 'jQuery',
                'pn': '1',
                'pz': '100',
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f164',  # 按人气值排序
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f164'
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html'
            }
            
            response = self.session.get(api_url, params=params, headers=headers, timeout=15)
            
            if response.status_code == 200:
                content = response.text
                
                # 解析JSONP
                if content.startswith('jQuery'):
                    start = content.find('(') + 1
                    end = content.rfind(')')
                    json_text = content[start:end]
                    data = json.loads(json_text)
                    
                    if 'data' in data and 'diff' in data['data']:
                        stocks = data['data']['diff']
                        
                        # 转换为标准格式
                        result = []
                        for i, stock in enumerate(stocks, 1):
                            stock_info = {
                                'rank': i,
                                'code': str(stock.get('f12', '')),
                                'name': str(stock.get('f14', '')),
                                'sector': self._get_stock_sector(str(stock.get('f12', ''))),
                                'price': float(stock.get('f2', 0)) if stock.get('f2') else 0,
                                'change_pct': float(stock.get('f3', 0)) if stock.get('f3') else 0,
                                'change_amount': float(stock.get('f4', 0)) if stock.get('f4') else 0,
                                'volume': int(stock.get('f5', 0)) if stock.get('f5') else 0,
                                'amount': float(stock.get('f6', 0)) if stock.get('f6') else 0,
                                'turnover': float(stock.get('f8', 0)) if stock.get('f8') else 0,
                                'popularity': int(stock.get('f164', 0)) if stock.get('f164') else 0
                            }
                            
                            # 数据验证
                            if stock_info['code'] and stock_info['name']:
                                result.append(stock_info)
                        
                        logger.info(f"API方式获取到 {len(result)} 只股票数据")
                        return result
            
        except Exception as e:
            logger.error(f"API方式获取数据失败: {e}")
        
        return None
    
    def _fetch_from_selenium(self, url):
        """使用Selenium从网页爬取数据"""
        if not self.init_selenium():
            logger.error("Selenium初始化失败")
            return None
        
        try:
            logger.info(f"访问页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(8)
            
            # 尝试多种选择器来找到股票数据
            selectors = [
                # 可能的股票列表选择器
                '.stock-list .stock-item',
                '.ranking-list .ranking-item',
                '.list-item',
                '[data-code]',
                '.stock-row',
                'tr[data-code]',
                '.stock-info',
                # 表格相关
                'table tbody tr',
                '.table-row',
                # 通用列表
                '.item',
                '[class*="stock"]',
                '[class*="rank"]'
            ]
            
            stocks_data = []
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and len(elements) > 5:  # 至少要有5个元素才认为是有效的
                        logger.info(f"找到股票元素: {selector} ({len(elements)}个)")
                        
                        for i, elem in enumerate(elements[:100], 1):  # 最多取100个
                            try:
                                text = elem.text.strip()
                                
                                # 尝试从文本中提取股票信息
                                stock_info = self._parse_stock_text(text, i)
                                if stock_info:
                                    stocks_data.append(stock_info)
                                    
                            except Exception as e:
                                continue
                        
                        if stocks_data:
                            logger.info(f"成功解析到 {len(stocks_data)} 只股票")
                            return stocks_data
                            
                except Exception as e:
                    continue
            
            # 如果没有找到结构化数据，尝试从页面源码中提取
            logger.info("尝试从页面源码中提取数据")
            page_source = self.driver.page_source
            
            # 查找可能的JSON数据
            json_pattern = re.compile(r'(\{[^{}]*"code"[^{}]*"name"[^{}]*\})', re.IGNORECASE)
            matches = json_pattern.findall(page_source)
            
            for match in matches[:10]:  # 只处理前10个匹配
                try:
                    data = json.loads(match)
                    if 'code' in data and 'name' in data:
                        stock_info = {
                            'rank': len(stocks_data) + 1,
                            'code': str(data.get('code', '')),
                            'name': str(data.get('name', '')),
                            'sector': self._get_stock_sector(str(data.get('code', ''))),
                            'price': float(data.get('price', 0)),
                            'change_pct': float(data.get('change_pct', 0)),
                            'change_amount': float(data.get('change_amount', 0)),
                            'volume': int(data.get('volume', 0)),
                            'amount': float(data.get('amount', 0)),
                            'turnover': float(data.get('turnover', 0)),
                            'popularity': int(data.get('popularity', 0))
                        }
                        stocks_data.append(stock_info)
                except:
                    continue
            
            if stocks_data:
                logger.info(f"从页面源码解析到 {len(stocks_data)} 只股票")
                return stocks_data
            
            logger.warning("未能从网页中提取到股票数据")
            return None
            
        except Exception as e:
            logger.error(f"Selenium爬取失败: {e}")
            return None
    
    def _parse_stock_text(self, text, rank):
        """从文本中解析股票信息"""
        if not text or len(text) < 10:
            return None
        
        # 查找股票代码（6位数字）
        code_match = re.search(r'\b(\d{6})\b', text)
        if not code_match:
            return None
        
        code = code_match.group(1)
        
        # 尝试提取股票名称（中文字符）
        name_match = re.search(r'([一-龯]{2,8})', text)
        name = name_match.group(1) if name_match else ''
        
        # 尝试提取价格和涨跌幅
        price_match = re.search(r'(\d+\.?\d*)', text)
        price = float(price_match.group(1)) if price_match else 0
        
        # 尝试提取涨跌幅（带%的数字）
        pct_match = re.search(r'([+-]?\d+\.?\d*)%', text)
        change_pct = float(pct_match.group(1)) if pct_match else 0
        
        if code and name:
            return {
                'rank': rank,
                'code': code,
                'name': name,
                'sector': self._get_stock_sector(code),
                'price': price,
                'change_pct': change_pct,
                'change_amount': 0,
                'volume': 0,
                'amount': 0,
                'turnover': 0,
                'popularity': 0
            }
        
        return None
    
    def close(self):
        """关闭资源"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
    
    def __del__(self):
        """析构函数"""
        self.close()
    
    def _get_stock_sector(self, code: str) -> str:
        """根据股票代码获取板块信息"""
        if not code or len(code) != 6:
            return "未知"
        
        # 尝试从网页获取板块信息
        try:
            sector = self._fetch_stock_sector_from_web(code)
            if sector and sector != "未知":
                return sector
        except:
            pass
        
        # 如果网页获取失败，使用本地映射
        sector_mapping = {
            # 主板
            '600': '主板',
            '601': '主板',
            '603': '主板',
            '605': '主板',
            '000': '主板',
            '001': '主板',
            '002': '主板',
            '003': '主板',
            
            # 创业板
            '300': '创业板',
            '301': '创业板',
            
            # 科创板
            '688': '科创板',
            '689': '科创板',
            
            # 北交所
            '430': '北交所',
            '830': '北交所',
            '831': '北交所',
            '832': '北交所',
            '833': '北交所',
            '834': '北交所',
            '835': '北交所',
            '836': '北交所',
            '837': '北交所',
            '838': '北交所',
            '839': '北交所',
            '870': '北交所',
            '871': '北交所',
            '872': '北交所',
            '873': '北交所',
            '874': '北交所',
            '875': '北交所',
            '876': '北交所',
            '877': '北交所',
            '878': '北交所',
            '879': '北交所',
        }
        
        # 根据前三位代码判断板块
        prefix = code[:3]
        return sector_mapping.get(prefix, "其他")
    
    def _fetch_stock_sector_from_web(self, code: str) -> str:
        """从东方财富网页获取股票板块信息"""
        try:
            # 构建股票详情页URL
            market = "1" if code.startswith(('600', '601', '603', '605', '688', '689')) else "0"
            url = f"https://quote.eastmoney.com/{market}{code}.html"
            
            # 使用requests获取页面
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 尝试多种选择器获取板块信息
                selectors = [
                    '.quote-info .quote-info-item:contains("所属行业") .quote-info-value',
                    '.stock-info .industry',
                    '.quote-info .industry',
                    '.stock-detail .industry',
                    '[data-field="industry"]',
                    '.industry-info',
                    '.sector-info',
                    '.concept-info'
                ]
                
                for selector in selectors:
                    try:
                        element = soup.select_one(selector)
                        if element:
                            text = element.get_text().strip()
                            if text and text != "未知":
                                return str(text).strip()
                    except:
                        continue
                
                # 尝试从页面源码中查找板块信息
                page_text = response.text
                
                # 查找行业信息
                industry_patterns = [
                    r'"industry":"([^"]+)"',
                    r'"所属行业":"([^"]+)"',
                    r'"行业":"([^"]+)"',
                    r'所属行业[：:]\s*([^<>\n]+)',
                    r'行业[：:]\s*([^<>\n]+)'
                ]
                
                for pattern in industry_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        industry = match.group(1).strip()
                        if industry and industry != "未知":
                            return str(industry).strip()
                
                # 查找概念信息
                concept_patterns = [
                    r'"concept":"([^"]+)"',
                    r'"概念":"([^"]+)"',
                    r'概念[：:]\s*([^<>\n]+)'
                ]
                
                for pattern in concept_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        concept = match.group(1).strip()
                        if concept and concept != "未知":
                            return str(concept).strip()
            
        except Exception as e:
            logger.debug(f"获取股票{code}板块信息失败: {e}")
        
        return "未知"
