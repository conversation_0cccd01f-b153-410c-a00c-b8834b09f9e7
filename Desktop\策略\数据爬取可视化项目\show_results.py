#!/usr/bin/env python3
"""
显示爬取结果
"""
import json

# 读取结果
with open('topic_stocks.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"=== 东方财富VIP话题榜数据 ===")
print(f"总共获取到 {len(data)} 个话题")
print()

for item in data:
    print(f"{item['rank']:2d}. {item['topic_title']}")
    print(f"    股票: {item['stock_name']} ({item['change_pct']:+.2f}%)")
    print(f"    阅读: {item['view_count']} | 讨论: {item['comment_count']}")
    print(f"    内容: {item['topic_content'][:100]}...")
    print()
