<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1,viewport-fit=cover"
    />
    <meta name="format-detection" content="email=no" />
    <meta name="format-detection" content="telephone=no" />
    <title>ä¸æ¹è´¢å¯ç­æ¦</title>
    <link rel="shortcut icon" type="image/x-icon" href="//g1.dfcfw.com/g1/special/favicon_shortcut.ico" />
      <!-- <script>
        window.onerror = function (a, b, c, d, e, f) {
            alert(a + "|" + b + "|" + c + "|" + d + "|" + e + "|" + f)
        }
    </script> -->
    <script>
      document.documentElement.style.fontSize =
        document.documentElement.clientWidth / 37.5 + "px";
    </script>
    <script type="text/javascript" charset="UTF-8">
      try {
        var emtj_isUpload = 1;
        var emtj_startTime = new Date().getTime();
        var emtj_logSet = '0100000000'; // å¨æåpageIDï¼è¿éçç¬¬ä¸ä¸ª1å°±è¦æ¹æ0ï¼è¦ä¸ç¶ä¼åä¸¤ä¸ªwebjs
        var emtj_sampleRate = 1;
      } catch (err) {}
    </script>
    <script
      type="text/javascript"
      src="https://bdstatics.eastmoney.com/web/prd/bigdata_tracker_top.js"
      charset="UTF-8"
    ></script>
  <link href=".././static/css/ranking\app\app.css?v=169cff9dbff61b7fecea" rel="stylesheet"><script type="text/javascript" src="../static/script/ranking\app\app_169cff9dbff61b7fecea.js?v=169cff9dbff61b7fecea"></script></head>

  <body>
    <script>
      var handleFontSize = function () {
        WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 2 });
        WeixinJSBridge.on('menu:setfont', function () {
          WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 2 });
        });
      };
      if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
        handleFontSize();
      } else {
        if (document.addEventListener) {
          document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
        } else if (document.attachEvent) {
          document.attachEvent("WeixinJSBridgeReady", handleFontSize);
          document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
        }
      }
    </script>
    <script type="text/javascript">
      if (/color=b/.test(navigator.userAgent) && !/uufund/i.test(navigator.userAgent)) {
        document.body.className += 'skinB';
      } else if (/color=d/.test(navigator.userAgent) && !/uufund/i.test(navigator.userAgent)) {
        document.body.className += 'skinW';
      } else if (/color=w/.test(navigator.userAgent)) {
        document.body.className += 'skinW';
      } else {
        document.body.className += 'skinW';
      }
    </script>
    <script>
      (function () {
        var docEl = document.documentElement;
        var resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';
        var recalc = function () {
          var clientWidth = docEl.clientWidth;
          clientWidth = clientWidth >= 500 ? clientWidth >= 800 ? clientWidth * 0.4 : clientWidth * 0.6 : clientWidth;
          if (!clientWidth) return;
          docEl.style.fontSize = 20 * (clientWidth / 750) + 'px';
        };
        if (!document.addEventListener) return;
        window.addEventListener(resizeEvt, recalc, false);
        document.addEventListener('DOMContentLoaded', recalc, false);
      })();
    </script>
    <div id="app"></div>
    <script type="text/javascript" src="https://bdstatics.eastmoney.com/web/prd/jump_tracker.js"></script>
  </body>
</html>
