"""
数据库管理模块
"""
import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional
from PyQt5.QtCore import QObject, pyqtSignal

from .config import DATABASE_PATH

class DatabaseManager(QObject):
    """数据库管理器"""
    
    # 信号定义
    data_saved = pyqtSignal(str, int)  # 数据类型, 保存数量
    error_occurred = pyqtSignal(str)   # 错误信息
    
    def __init__(self):
        super().__init__()
        self.db_path = DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建人气榜表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS popularity_ranking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        rank_num INTEGER,
                        stock_code TEXT,
                        stock_name TEXT,
                        price REAL,
                        change_pct REAL,
                        change_amount REAL,
                        volume INTEGER,
                        amount INTEGER,
                        popularity INTEGER,
                        update_time TEXT,
                        UNIQUE(stock_code, update_time)
                    )
                ''')
                
                # 创建飙升榜表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS soaring_ranking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        rank_num INTEGER,
                        stock_code TEXT,
                        stock_name TEXT,
                        price REAL,
                        change_pct REAL,
                        change_amount REAL,
                        volume INTEGER,
                        amount INTEGER,
                        turnover REAL,
                        update_time TEXT,
                        UNIQUE(stock_code, update_time)
                    )
                ''')
                
                # 创建更新日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS update_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT,
                        update_time TEXT,
                        records_count INTEGER,
                        status TEXT
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.error_occurred.emit(f"数据库初始化失败: {str(e)}")
    
    def save_popularity_data(self, data: List[Dict]) -> bool:
        """保存人气榜数据"""
        return self._save_ranking_data(data, 'popularity_ranking', 'popularity')
    
    def save_soaring_data(self, data: List[Dict]) -> bool:
        """保存飙升榜数据"""
        return self._save_ranking_data(data, 'soaring_ranking', 'soaring')
    
    def _save_ranking_data(self, data: List[Dict], table_name: str, data_type: str) -> bool:
        """保存排行榜数据"""
        if not data:
            return False
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 清除当前时间段的旧数据（保留历史数据）
                time_key = current_time[:16]  # 精确到分钟
                cursor.execute(f'DELETE FROM {table_name} WHERE update_time LIKE ?', (f'{time_key}%',))
                
                # 插入新数据
                if table_name == 'popularity_ranking':
                    for stock in data:
                        cursor.execute('''
                            INSERT OR REPLACE INTO popularity_ranking 
                            (rank_num, stock_code, stock_name, price, change_pct, change_amount, 
                             volume, amount, popularity, update_time)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            stock.get('rank', 0),
                            stock.get('code', ''),
                            stock.get('name', ''),
                            stock.get('price', 0),
                            stock.get('change_pct', 0),
                            stock.get('change_amount', 0),
                            stock.get('volume', 0),
                            stock.get('amount', 0),
                            stock.get('popularity', 0),
                            current_time
                        ))
                else:  # soaring_ranking
                    for stock in data:
                        cursor.execute('''
                            INSERT OR REPLACE INTO soaring_ranking 
                            (rank_num, stock_code, stock_name, price, change_pct, change_amount, 
                             volume, amount, turnover, update_time)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            stock.get('rank', 0),
                            stock.get('code', ''),
                            stock.get('name', ''),
                            stock.get('price', 0),
                            stock.get('change_pct', 0),
                            stock.get('change_amount', 0),
                            stock.get('volume', 0),
                            stock.get('amount', 0),
                            stock.get('turnover', 0),
                            current_time
                        ))
                
                # 记录更新日志
                cursor.execute('''
                    INSERT INTO update_log (table_name, update_time, records_count, status)
                    VALUES (?, ?, ?, ?)
                ''', (table_name, current_time, len(data), 'SUCCESS'))
                
                conn.commit()
                self.data_saved.emit(data_type, len(data))
                return True
                
        except Exception as e:
            self.error_occurred.emit(f"保存{data_type}数据失败: {str(e)}")
            return False
    
    def get_latest_popularity_data(self) -> List[Dict]:
        """获取最新人气榜数据"""
        return self._get_latest_ranking_data('popularity_ranking')
    
    def get_latest_soaring_data(self) -> List[Dict]:
        """获取最新飙升榜数据"""
        return self._get_latest_ranking_data('soaring_ranking')
    
    def _get_latest_ranking_data(self, table_name: str) -> List[Dict]:
        """获取最新排行榜数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if table_name == 'popularity_ranking':
                    cursor.execute('''
                        SELECT rank_num, stock_code, stock_name, price, change_pct, change_amount,
                               volume, amount, popularity, update_time
                        FROM popularity_ranking
                        WHERE update_time = (SELECT MAX(update_time) FROM popularity_ranking)
                        ORDER BY rank_num
                    ''')
                    
                    columns = ['rank', 'code', 'name', 'price', 'change_pct', 'change_amount',
                              'volume', 'amount', 'popularity', 'update_time']
                else:
                    cursor.execute('''
                        SELECT rank_num, stock_code, stock_name, price, change_pct, change_amount,
                               volume, amount, turnover, update_time
                        FROM soaring_ranking
                        WHERE update_time = (SELECT MAX(update_time) FROM soaring_ranking)
                        ORDER BY rank_num
                    ''')
                    
                    columns = ['rank', 'code', 'name', 'price', 'change_pct', 'change_amount',
                              'volume', 'amount', 'turnover', 'update_time']
                
                rows = cursor.fetchall()
                
                # 转换为字典列表
                result = []
                for row in rows:
                    stock_dict = dict(zip(columns, row))
                    result.append(stock_dict)
                
                return result
                
        except Exception as e:
            self.error_occurred.emit(f"获取数据失败: {str(e)}")
            return []
    
    def get_last_update_time(self) -> Optional[str]:
        """获取最后更新时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT MAX(update_time) FROM update_log WHERE status = 'SUCCESS'
                ''')
                result = cursor.fetchone()
                return result[0] if result and result[0] else None
                
        except Exception as e:
            self.error_occurred.emit(f"获取更新时间失败: {str(e)}")
            return None
    
    def export_to_excel(self, file_path: str, data_type: str = 'both') -> bool:
        """导出数据到Excel"""
        try:
            import pandas as pd
            
            with sqlite3.connect(self.db_path) as conn:
                if data_type == 'both' or data_type == 'popularity':
                    # 导出人气榜
                    popularity_df = pd.read_sql_query('''
                        SELECT rank_num as 排名, stock_code as 股票代码, stock_name as 股票名称,
                               price as 最新价, change_pct as 涨跌幅, change_amount as 涨跌额,
                               volume as 成交量, amount as 成交额, popularity as 人气值,
                               update_time as 更新时间
                        FROM popularity_ranking
                        WHERE update_time = (SELECT MAX(update_time) FROM popularity_ranking)
                        ORDER BY rank_num
                    ''', conn)
                
                if data_type == 'both' or data_type == 'soaring':
                    # 导出飙升榜
                    soaring_df = pd.read_sql_query('''
                        SELECT rank_num as 排名, stock_code as 股票代码, stock_name as 股票名称,
                               price as 最新价, change_pct as 涨跌幅, change_amount as 涨跌额,
                               volume as 成交量, amount as 成交额, turnover as 换手率,
                               update_time as 更新时间
                        FROM soaring_ranking
                        WHERE update_time = (SELECT MAX(update_time) FROM soaring_ranking)
                        ORDER BY rank_num
                    ''', conn)
                
                # 写入Excel文件
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    if data_type == 'both':
                        popularity_df.to_excel(writer, sheet_name='人气榜', index=False)
                        soaring_df.to_excel(writer, sheet_name='飙升榜', index=False)
                    elif data_type == 'popularity':
                        popularity_df.to_excel(writer, sheet_name='人气榜', index=False)
                    elif data_type == 'soaring':
                        soaring_df.to_excel(writer, sheet_name='飙升榜', index=False)
                
                return True
                
        except Exception as e:
            self.error_occurred.emit(f"导出Excel失败: {str(e)}")
            return False
