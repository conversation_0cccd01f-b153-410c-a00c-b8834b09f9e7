"""
东方财富数据爬虫
"""
import requests
import json
import re
import time
import logging
from typing import List, Dict, Optional
from PyQt5.QtCore import QObject, pyqtSignal

from .config import HEADERS, EASTMONEY_URLS, RETRY_COUNT, RETRY_DELAY
from .web_scraper import EastMoneyWebScraper

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EastMoneyScraper(QObject):
    """东方财富数据爬虫"""
    
    # 信号定义
    data_updated = pyqtSignal(str, list)  # 数据类型, 数据列表
    error_occurred = pyqtSignal(str)      # 错误信息
    status_changed = pyqtSignal(str)      # 状态信息
    
    def __init__(self):
        super().__init__()
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
        self.web_scraper = None  # 网页爬虫实例
    
    def _make_request(self, url: str) -> Optional[str]:
        """发送HTTP请求"""
        for attempt in range(RETRY_COUNT):
            try:
                self.status_changed.emit(f"正在请求数据... (尝试 {attempt + 1}/{RETRY_COUNT})")
                
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                
                return response.text
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}): {e}")
                if attempt < RETRY_COUNT - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    self.error_occurred.emit(f"网络请求失败: {str(e)}")
                    
        return None
    
    def _parse_jsonp_response(self, text: str) -> Optional[Dict]:
        """解析JSONP响应"""
        try:
            # 移除JSONP回调函数包装
            if text.startswith('jQuery'):
                start = text.find('(') + 1
                end = text.rfind(')')
                text = text[start:end]
            
            return json.loads(text)
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"JSON解析失败: {e}")
            self.error_occurred.emit(f"数据解析失败: {str(e)}")
            return None
    
    def _parse_stock_data(self, raw_data: Dict, data_type: str) -> List[Dict]:
        """解析股票数据"""
        try:
            if not raw_data or 'data' not in raw_data:
                return []
            
            data = raw_data['data']
            if not data or 'diff' not in data:
                return []
            
            stocks = data['diff']
            parsed_stocks = []
            
            for i, stock in enumerate(stocks, 1):
                try:
                    # 解析股票信息 - 支持两种API格式
                    if data_type == 'popularity' and 'f148' in stock:
                        # VIP人气榜API格式
                        stock_info = {
                            'rank': i,
                            'code': str(stock.get('f12', '')),  # 股票代码
                            'name': str(stock.get('f14', '')),  # 股票名称
                            'sector': self._get_stock_sector(str(stock.get('f12', ''))),  # 板块
                            'price': float(stock.get('f2', 0)) if stock.get('f2') else 0,   # 最新价
                            'change_pct': float(stock.get('f3', 0)) if stock.get('f3') else 0,  # 涨跌幅
                            'change_amount': float(stock.get('f4', 0)) if stock.get('f4') else 0,  # 涨跌额
                            'volume': int(stock.get('f5', 0)) if stock.get('f5') else 0,  # 成交量(手)
                            'amount': float(stock.get('f6', 0)) if stock.get('f6') else 0,  # 成交额(元)
                            'turnover': float(stock.get('f8', 0)) if stock.get('f8') else 0,  # 换手率
                            'popularity': int(stock.get('f148', 0)) if stock.get('f148') else 0  # 人气值
                        }
                    else:
                        # 原有API格式
                        stock_info = {
                            'rank': i,
                            'code': str(stock.get('f12', '')),  # 股票代码
                            'name': str(stock.get('f14', '')),  # 股票名称
                            'sector': self._get_stock_sector(str(stock.get('f12', ''))),  # 板块
                            'price': float(stock.get('f2', 0)) if stock.get('f2') else 0,   # 最新价
                            'change_pct': float(stock.get('f3', 0)) if stock.get('f3') else 0,  # 涨跌幅
                            'change_amount': float(stock.get('f4', 0)) if stock.get('f4') else 0,  # 涨跌额
                            'volume': int(stock.get('f5', 0)) if stock.get('f5') else 0,  # 成交量(手)
                            'amount': float(stock.get('f6', 0)) if stock.get('f6') else 0,  # 成交额(元)
                            'turnover': float(stock.get('f8', 0)) if stock.get('f8') else 0,  # 换手率
                            'popularity': int(stock.get('f164', 0)) if data_type == 'popularity' and stock.get('f164') else 0  # 人气值
                        }
                    
                    # 数据验证和清理
                    if not stock_info['code'] or not stock_info['name']:
                        continue  # 跳过无效数据
                    
                    parsed_stocks.append(stock_info)
                    
                except (KeyError, ValueError, TypeError) as e:
                    logger.warning(f"解析第{i}条股票数据失败: {e}")
                    continue
            
            return parsed_stocks
            
        except Exception as e:
            logger.error(f"解析股票数据失败: {e}")
            self.error_occurred.emit(f"数据解析失败: {str(e)}")
            return []
    
    def fetch_popularity_ranking(self) -> List[Dict]:
        """获取人气榜数据"""
        self.status_changed.emit("正在获取人气榜数据...")
        
        # 尝试主要API
        url = EASTMONEY_URLS['popularity']
        response_text = self._make_request(url)
        
        if response_text:
            raw_data = self._parse_jsonp_response(response_text)
            if raw_data:
                stocks = self._parse_stock_data(raw_data, 'popularity')
                if stocks:
                    self.status_changed.emit(f"成功获取人气榜数据: {len(stocks)}只股票")
                    self.data_updated.emit('popularity', stocks)
                    return stocks
        
        # 尝试备用API
        self.status_changed.emit("尝试备用数据源...")
        backup_url = EASTMONEY_URLS['backup_popularity']
        response_text = self._make_request(backup_url)
        
        if response_text:
            try:
                raw_data = json.loads(response_text)
                # 这里需要根据备用API的实际响应格式进行解析
                # 暂时返回空列表
                self.status_changed.emit("备用数据源暂不可用")
            except:
                pass
        
        self.error_occurred.emit("无法获取人气榜数据")
        return []
    
    def fetch_soaring_ranking(self) -> List[Dict]:
        """获取飙升榜数据"""
        self.status_changed.emit("正在获取飙升榜数据...")
        
        # 首先尝试网页爬取飙升榜
        web_data = self._fetch_soaring_from_web()
        if web_data:
            self.status_changed.emit(f"飙升榜网页爬取成功，获取到 {len(web_data)} 只股票")
            self.data_updated.emit('soaring', web_data)
            return web_data
        
        # 尝试主要API
        url = EASTMONEY_URLS['soaring']
        response_text = self._make_request(url)
        
        if response_text:
            raw_data = self._parse_jsonp_response(response_text)
            if raw_data:
                stocks = self._parse_stock_data(raw_data, 'soaring')
                if stocks:
                    self.status_changed.emit(f"成功获取飙升榜数据: {len(stocks)}只股票")
                    self.data_updated.emit('soaring', stocks)
                    return stocks
        
        # 尝试备用API
        self.status_changed.emit("尝试备用数据源...")
        backup_url = EASTMONEY_URLS['backup_soaring']
        response_text = self._make_request(backup_url)
        
        if response_text:
            try:
                raw_data = json.loads(response_text)
                # 这里需要根据备用API的实际响应格式进行解析
                # 暂时返回空列表
                self.status_changed.emit("备用数据源暂不可用")
            except:
                pass
        
        self.error_occurred.emit("无法获取飙升榜数据")
        return []

    def _fetch_from_web(self) -> List[Dict]:
        """从网页爬取人气榜数据"""
        try:
            import subprocess
            import json
            import os
            
            # 运行网页爬虫脚本
            script_path = os.path.join(os.path.dirname(__file__), '..', 'extract_vip_stocks.py')
            if os.path.exists(script_path):
                result = subprocess.run(['python', script_path], 
                                      capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    # 读取生成的JSON文件
                    json_path = os.path.join(os.path.dirname(__file__), '..', 'vip_stocks.json')
                    if os.path.exists(json_path):
                        with open(json_path, 'r', encoding='utf-8') as f:
                            stocks = json.load(f)
                        
                        # 转换为标准格式
                        formatted_stocks = []
                        for stock in stocks:
                            formatted_stock = {
                                'rank': stock['rank'],
                                'code': stock['code'],
                                'name': stock['name'],
                                'price': stock['price'],
                                'change_pct': stock['change_pct'],
                                'change_amount': stock.get('change_amount', 0),
                                'volume': stock.get('volume', 0),
                                'amount': stock.get('amount', 0),
                                'turnover': stock.get('turnover', 0),
                                'popularity': stock.get('popularity', stock['rank'])
                            }
                            formatted_stocks.append(formatted_stock)
                        
                        logger.info(f"从网页爬取到人气榜 {len(formatted_stocks)} 只股票")
                        return formatted_stocks
            
            return []
            
        except Exception as e:
            logger.error(f"人气榜网页爬取失败: {e}")
            return []

    def _fetch_soaring_from_web(self) -> List[Dict]:
        """从网页爬取飙升榜数据"""
        try:
            import subprocess
            import json
            import os
            
            # 运行飙升榜网页爬虫脚本
            script_path = os.path.join(os.path.dirname(__file__), '..', 'extract_soaring_stocks.py')
            if os.path.exists(script_path):
                result = subprocess.run(['python', script_path], 
                                      capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    # 读取生成的JSON文件
                    json_path = os.path.join(os.path.dirname(__file__), '..', 'soaring_stocks.json')
                    if os.path.exists(json_path):
                        with open(json_path, 'r', encoding='utf-8') as f:
                            stocks = json.load(f)
                        
                        # 转换为标准格式
                        formatted_stocks = []
                        for stock in stocks:
                            formatted_stock = {
                                'rank': stock['rank'],
                                'code': stock['code'],
                                'name': stock['name'],
                                'sector': self._get_stock_sector(stock['code']),
                                'price': stock['price'],
                                'change_pct': stock['change_pct'],
                                'change_amount': stock.get('change_amount', 0),
                                'volume': stock.get('volume', 0),
                                'amount': stock.get('amount', 0),
                                'turnover': stock.get('turnover', 0),
                                'popularity': stock.get('popularity', stock['rank'])
                            }
                            formatted_stocks.append(formatted_stock)
                        
                        logger.info(f"从网页爬取到飙升榜 {len(formatted_stocks)} 只股票")
                        return formatted_stocks
            
            return []
            
        except Exception as e:
            logger.error(f"飙升榜网页爬取失败: {e}")
            return []

    def fetch_hotstock_from_web(self) -> List[Dict]:
        """从网页直接爬取热股榜数据"""
        self.status_changed.emit("正在从网页爬取热股榜数据...")

        try:
            # 初始化网页爬虫
            if self.web_scraper is None:
                self.web_scraper = EastMoneyWebScraper()

            # 从网页获取数据
            data = self.web_scraper.fetch_hotstock_from_web()

            if data:
                self.status_changed.emit(f"网页爬取成功，获取到 {len(data)} 只股票")
                self.data_updated.emit('hotstock', data)
                return data
            else:
                self.error_occurred.emit("网页爬取失败，未获取到数据")
                return []

        except Exception as e:
            error_msg = f"网页爬取失败: {str(e)}"
            self.error_occurred.emit(error_msg)
            return []

    def fetch_all_data(self):
        """获取所有数据"""
        self.status_changed.emit("开始更新数据...")
        
        # 获取人气榜
        popularity_data = self.fetch_popularity_ranking()
        
        # 短暂延迟避免请求过于频繁
        time.sleep(1)
        
        # 获取飙升榜
        soaring_data = self.fetch_soaring_ranking()
        
        if popularity_data or soaring_data:
            self.status_changed.emit("数据更新完成")
        else:
            self.status_changed.emit("数据更新失败")
    
    def format_number(self, num: float, unit: str = '') -> str:
        """格式化数字显示"""
        if num == 0:
            return '0'
        
        if abs(num) >= 100000000:  # 亿
            return f"{num/100000000:.2f}亿{unit}"
        elif abs(num) >= 10000:    # 万
            return f"{num/10000:.2f}万{unit}"
        else:
            return f"{num:.2f}{unit}"
    
    def _get_stock_sector(self, code: str) -> str:
        """根据股票代码获取板块信息"""
        if not code or len(code) != 6:
            return "未知"
        
        # 使用本地行业板块映射
        sector = self._get_industry_from_mapping(code)
        if sector and sector != "未知":
            return sector
        
        # 如果本地映射没有，尝试API获取
        try:
            sector = self._fetch_industry_from_api(code)
            if sector and sector != "未知":
                return sector
        except:
            pass
        
        # 如果都失败，返回简单分类
        prefix = code[:3]
        if prefix in ['600', '601', '603', '605', '000', '001', '002', '003']:
            return "主板"
        elif prefix in ['300', '301']:
            return "创业板"
        elif prefix in ['688', '689']:
            return "科创板"
        else:
            return "其他"
    
    def _get_industry_from_mapping(self, code: str) -> str:
        """从本地映射获取行业信息"""
        # 常见股票的行业映射
        industry_mapping = {
            # 科技股
            '000001': '银行',
            '000002': '房地产',
            '000858': '白酒',
            '000596': '白酒',
            '000799': '白酒',
            '000568': '白酒',
            '000858': '白酒',
            '000596': '白酒',
            '000799': '白酒',
            '000568': '白酒',
            
            # 新能源
            '300750': '新能源',
            '002594': '新能源',
            '002460': '新能源',
            '300274': '新能源',
            '002709': '新能源',
            '300763': '新能源',
            '300207': '新能源',
            '002074': '新能源',
            '300316': '新能源',
            '002129': '新能源',
            
            # 医药
            '000001': '银行',
            '000002': '房地产',
            '000858': '白酒',
            '000596': '白酒',
            '000799': '白酒',
            '000568': '白酒',
            
            # 芯片
            '000725': '芯片',
            '002049': '芯片',
            '300661': '芯片',
            '300782': '芯片',
            '002371': '芯片',
            '300671': '芯片',
            '300223': '芯片',
            '002156': '芯片',
            '300373': '芯片',
            '300458': '芯片',
            
            # 人工智能
            '002230': '人工智能',
            '300253': '人工智能',
            '002415': '人工智能',
            '300496': '人工智能',
            '002008': '人工智能',
            '300033': '人工智能',
            '002475': '人工智能',
            '300059': '人工智能',
            '002230': '人工智能',
            '300253': '人工智能',
            
            # 军工
            '000768': '军工',
            '002013': '军工',
            '002179': '军工',
            '002023': '军工',
            '002151': '军工',
            '002465': '军工',
            '002414': '军工',
            '002389': '军工',
            '002297': '军工',
            '002013': '军工',
            
            # 消费电子
            '002475': '消费电子',
            '002241': '消费电子',
            '002456': '消费电子',
            '002600': '消费电子',
            '002273': '消费电子',
            '002008': '消费电子',
            '002475': '消费电子',
            '002241': '消费电子',
            '002456': '消费电子',
            '002600': '消费电子',
        }
        
        return industry_mapping.get(code, "未知")
    
    def _fetch_sector_from_industry_page(self, code: str) -> str:
        """从东方财富行业板块页面获取股票板块信息"""
        try:
            # 使用东方财富行业板块API
            url = "https://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'cb': 'jQuery',
                'pn': '1',
                'pz': '5000',  # 获取足够多的数据
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f3',
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f12,f14,f57,f58'  # 代码,名称,行业,概念
            }
            
            response = self.session.get(url, params=params, timeout=10)
            if response.status_code == 200:
                # 解析JSONP响应
                text = response.text
                if text.startswith('jQuery'):
                    start = text.find('(') + 1
                    end = text.rfind(')')
                    json_text = text[start:end]
                    data = json.loads(json_text)
                    
                    if 'data' in data and 'diff' in data['data']:
                        stocks = data['data']['diff']
                        
                        # 查找目标股票
                        for stock in stocks:
                            if str(stock.get('f12', '')) == code:
                                # 优先返回行业信息
                                industry = stock.get('f57', '')
                                if industry:
                                    return str(industry).strip()
                                
                                # 如果没有行业信息，返回概念信息
                                concept = stock.get('f58', '')
                                if concept:
                                    return str(concept).strip()
                                
                                break
            
        except Exception as e:
            logger.debug(f"从行业页面获取股票{code}板块信息失败: {e}")
        
        return "未知"
    
    def _fetch_stock_sector_from_api(self, code: str) -> str:
        """从东方财富网页获取股票行业板块信息"""
        try:
            # 构建股票详情页URL
            market = "1" if code.startswith(('600', '601', '603', '605', '688', '689')) else "0"
            url = f"https://quote.eastmoney.com/{market}{code}.html"
            
            # 使用requests获取页面
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 尝试多种选择器获取板块信息
                selectors = [
                    '.quote-info .quote-info-item:contains("所属行业") .quote-info-value',
                    '.stock-info .industry',
                    '.quote-info .industry',
                    '.stock-detail .industry',
                    '[data-field="industry"]',
                    '.industry-info',
                    '.sector-info',
                    '.concept-info'
                ]
                
                for selector in selectors:
                    try:
                        element = soup.select_one(selector)
                        if element:
                            text = element.get_text().strip()
                            if text and text != "未知":
                                return str(text).strip()
                    except:
                        continue
                
                # 尝试从页面源码中查找板块信息
                page_text = response.text
                
                # 查找行业信息
                industry_patterns = [
                    r'"industry":"([^"]+)"',
                    r'"所属行业":"([^"]+)"',
                    r'"行业":"([^"]+)"',
                    r'所属行业[：:]\s*([^<>\n]+)',
                    r'行业[：:]\s*([^<>\n]+)'
                ]
                
                for pattern in industry_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        industry = match.group(1).strip()
                        if industry and industry != "未知":
                            return str(industry).strip()
                
                # 查找概念信息
                concept_patterns = [
                    r'"concept":"([^"]+)"',
                    r'"概念":"([^"]+)"',
                    r'概念[：:]\s*([^<>\n]+)'
                ]
                
                for pattern in concept_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        concept = match.group(1).strip()
                        if concept and concept != "未知":
                            return str(concept).strip()
            
        except Exception as e:
            logger.debug(f"获取股票{code}板块信息失败: {e}")
        
        return "未知"
    
    def _fetch_industry_from_api(self, code: str) -> str:
        """从东方财富API直接获取股票行业信息"""
        try:
            # 使用东方财富行业板块API
            url = "https://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'cb': 'jQuery',
                'pn': '1',
                'pz': '5000',  # 获取足够多的数据
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f3',
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f12,f14,f57,f58'  # 代码,名称,行业,概念
            }
            
            response = self.session.get(url, params=params, timeout=10)
            if response.status_code == 200:
                # 解析JSONP响应
                text = response.text
                if text.startswith('jQuery'):
                    start = text.find('(') + 1
                    end = text.rfind(')')
                    json_text = text[start:end]
                    data = json.loads(json_text)
                    
                    if 'data' in data and 'diff' in data['data']:
                        stocks = data['data']['diff']
                        
                        # 查找目标股票
                        for stock in stocks:
                            if str(stock.get('f12', '')) == code:
                                # 优先返回行业信息
                                industry = stock.get('f57', '')
                                if industry and industry != '':
                                    return str(industry).strip()
                                
                                # 如果没有行业信息，返回概念信息
                                concept = stock.get('f58', '')
                                if concept and concept != '':
                                    return str(concept).strip()
                                
                                break
            
        except Exception as e:
            logger.debug(f"获取股票{code}行业信息失败: {e}")
        
        return "未知"
