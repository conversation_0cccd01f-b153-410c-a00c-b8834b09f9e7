"""
主窗口
"""
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QLabel, QGroupBox, QStatusBar, 
                             QSplitter, QLineEdit, QMessageBox, QFileDialog,
                             QProgressBar, QFrame)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

from .ranking_table import RankingTableWidget
from core.scraper import EastMoneyScraper
from core.database import DatabaseManager
from core.config import (WINDOW_TITLE, WINDOW_WIDTH, WINDOW_HEIGHT, 
                        MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT, UPDATE_INTERVAL)

class DataUpdateThread(QThread):
    """数据更新线程"""
    finished = pyqtSignal()
    
    def __init__(self, scraper):
        super().__init__()
        self.scraper = scraper
    
    def run(self):
        """运行数据更新"""
        self.scraper.fetch_all_data()
        self.finished.emit()

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.scraper = EastMoneyScraper()
        self.db_manager = DatabaseManager()
        self.update_thread = None
        self.setup_ui()
        self.setup_timer()
        self.connect_signals()
        self.load_cached_data()
    
    def setup_ui(self):
        """设置界面"""
        # 设置窗口属性
        self.setWindowTitle(WINDOW_TITLE)
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)
        self.setMinimumSize(MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建超紧凑主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(2)
        main_layout.setContentsMargins(2, 2, 2, 2)

        # 创建合并的顶部栏（工具栏+搜索栏合并）
        top_bar_layout = self.create_compact_top_bar()
        main_layout.addLayout(top_bar_layout)

        # 创建表格区域（占用大部分空间）
        tables_widget = self.create_tables()
        main_layout.addWidget(tables_widget, 1)  # 设置拉伸因子为1，占用剩余空间
        
        # 创建状态栏
        self.create_status_bar()
    


    def create_compact_top_bar(self):
        """创建超紧凑的顶部栏（工具栏+搜索栏合并）"""
        # 创建顶部栏容器
        top_bar_widget = QWidget()
        top_bar_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 6px;
                padding: 2px;
            }
        """)
        top_bar_widget.setMaximumHeight(50)  # 增加高度

        layout = QHBoxLayout(top_bar_widget)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 5, 8, 5)

        # 标题（增大字体）
        title_label = QLabel("📊 东方财富榜单")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 700;
                color: #2c3e50;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)

        # 状态指示器（增大字体）
        self.status_label = QLabel("🔄 获取中")
        self.status_label.setStyleSheet("""
            QLabel {
                background: #fff3e0;
                color: #ef6c00;
                font-weight: 600;
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 12px;
                min-width: 60px;
            }
        """)
        layout.addWidget(self.status_label)

        # 搜索框（增大字体）
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索股票代码或名称...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 1px solid #e1e8ed;
                border-radius: 15px;
                padding: 6px 12px;
                font-size: 13px;
                color: #2c3e50;
                max-width: 200px;
                min-height: 24px;
            }
            QLineEdit:focus {
                border-color: #4285f4;
                background-color: white;
            }
        """)
        self.search_input.textChanged.connect(self.on_search_text_changed)
        layout.addWidget(self.search_input)

        # 清除按钮（增大）
        clear_btn = QPushButton("✕")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #f44336;
                color: white;
                border: none;
                min-width: 24px;
                max-width: 24px;
                height: 24px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #ef5350;
            }
        """)
        clear_btn.clicked.connect(self.clear_search)
        layout.addWidget(clear_btn)

        # 添加弹性空间
        layout.addStretch()

        # 统计信息（增大字体）
        self.stats_label = QLabel("等待加载...")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-size: 12px;
                font-weight: 500;
                background: #f1f3f4;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        layout.addWidget(self.stats_label)

        # 按钮区域（增大按钮）
        button_layout = QHBoxLayout()
        button_layout.setSpacing(4)

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄")
        self.refresh_btn.setToolTip("刷新数据 (F5)")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: #34a853;
                color: white;
                border: none;
                min-width: 30px;
                max-width: 30px;
                height: 30px;
                border-radius: 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #46b566;
            }
        """)
        self.refresh_btn.clicked.connect(self.manual_refresh)
        button_layout.addWidget(self.refresh_btn)

        # 导出按钮
        self.export_btn = QPushButton("📊")
        self.export_btn.setToolTip("导出Excel")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background: #ff9800;
                color: white;
                border: none;
                min-width: 30px;
                max-width: 30px;
                height: 30px;
                border-radius: 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #ffb74d;
            }
        """)
        self.export_btn.clicked.connect(self.export_data)
        button_layout.addWidget(self.export_btn)

        # 设置按钮
        self.settings_btn = QPushButton("⚙️")
        self.settings_btn.setToolTip("设置")
        self.settings_btn.setStyleSheet("""
            QPushButton {
                background: #9c27b0;
                color: white;
                border: none;
                min-width: 30px;
                max-width: 30px;
                height: 30px;
                border-radius: 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #ba68c8;
            }
        """)
        self.settings_btn.clicked.connect(self.open_settings)
        button_layout.addWidget(self.settings_btn)

        layout.addLayout(button_layout)

        # 创建容器布局
        container_layout = QVBoxLayout()
        container_layout.addWidget(top_bar_widget)
        container_layout.setContentsMargins(0, 0, 0, 0)

        return container_layout

    def create_search_bar(self):
        """创建紧凑型搜索栏"""
        # 创建搜索栏容器
        search_widget = QWidget()
        search_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                padding: 4px;
            }
        """)

        layout = QHBoxLayout(search_widget)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(8)

        # 搜索图标
        search_icon_label = QLabel("🔍")
        search_icon_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #5f6368;
            }
        """)
        layout.addWidget(search_icon_label)

        # 搜索输入框（紧凑版）
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索股票代码或名称...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 1px solid #e1e8ed;
                border-radius: 16px;
                padding: 6px 12px;
                font-size: 12px;
                color: #2c3e50;
                min-width: 200px;
                max-height: 24px;
            }
            QLineEdit:focus {
                border-color: #4285f4;
                background-color: white;
            }
            QLineEdit::placeholder {
                color: #95a5a6;
            }
        """)
        self.search_input.textChanged.connect(self.on_search_text_changed)
        layout.addWidget(self.search_input)

        # 清除搜索按钮（紧凑版）
        clear_btn = QPushButton("✕")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #f44336;
                color: white;
                border: none;
                min-width: 24px;
                max-width: 24px;
                height: 24px;
                border-radius: 12px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #ef5350;
            }
        """)
        clear_btn.clicked.connect(self.clear_search)
        layout.addWidget(clear_btn)

        # 添加弹性空间
        layout.addStretch()

        # 统计信息（紧凑版）
        self.stats_label = QLabel("等待加载...")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-size: 10px;
                font-weight: 500;
                background: #f1f3f4;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        layout.addWidget(self.stats_label)

        # 创建容器布局
        container_layout = QVBoxLayout()
        container_layout.addWidget(search_widget)
        container_layout.setContentsMargins(0, 0, 0, 0)

        return container_layout
    
    def create_tables(self):
        """创建两列并排表格区域"""
        # 创建水平分割器（两列布局）
        splitter = QSplitter(Qt.Horizontal)
        splitter.setHandleWidth(6)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background: #e1e8ed;
                border-radius: 3px;
                margin: 2px;
            }
            QSplitter::handle:hover {
                background: #4285f4;
            }
        """)

        # 创建人气榜组（紧凑版）
        popularity_group = QGroupBox("📈 VIP人气榜")
        popularity_group.setStyleSheet("""
            QGroupBox::title {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b6b, stop:1 #ee5a24);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 700;
                margin-left: 8px;
            }
        """)

        popularity_layout = QVBoxLayout(popularity_group)
        popularity_layout.setContentsMargins(8, 16, 8, 8)
        popularity_layout.setSpacing(4)

        # 人气榜统计信息（紧凑版）
        self.popularity_stats = QLabel("📊 加载中...")
        self.popularity_stats.setStyleSheet("""
            QLabel {
                background: #fff5f5;
                color: #c53030;
                padding: 4px 8px;
                border-radius: 6px;
                font-size: 10px;
                font-weight: 500;
                border: 1px solid #feb2b2;
            }
        """)
        popularity_layout.addWidget(self.popularity_stats)

        self.popularity_table = RankingTableWidget('popularity')
        popularity_layout.addWidget(self.popularity_table)

        # 创建飙升榜组（紧凑版）
        soaring_group = QGroupBox("🔥 VIP飙升榜")
        soaring_group.setStyleSheet("""
            QGroupBox::title {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4ecdc4, stop:1 #44a08d);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 700;
                margin-left: 8px;
            }
        """)

        soaring_layout = QVBoxLayout(soaring_group)
        soaring_layout.setContentsMargins(8, 16, 8, 8)
        soaring_layout.setSpacing(4)

        # 飙升榜统计信息（紧凑版）
        self.soaring_stats = QLabel("📊 加载中...")
        self.soaring_stats.setStyleSheet("""
            QLabel {
                background: #f0fff4;
                color: #276749;
                padding: 4px 8px;
                border-radius: 6px;
                font-size: 10px;
                font-weight: 500;
                border: 1px solid #9ae6b4;
            }
        """)
        soaring_layout.addWidget(self.soaring_stats)

        self.soaring_table = RankingTableWidget('soaring')
        soaring_layout.addWidget(self.soaring_table)

        # 添加到分割器
        splitter.addWidget(popularity_group)
        splitter.addWidget(soaring_group)

        # 设置分割比例（水平分割，两列等宽）
        splitter.setSizes([400, 400])

        return splitter
    
    def create_status_bar(self):
        """创建现代化状态栏"""
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-top: 2px solid #e1e8ed;
                color: #5f6368;
                font-size: 12px;
                padding: 8px;
            }
        """)
        self.setStatusBar(self.status_bar)

        # 创建状态栏容器
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(16, 4, 16, 4)
        status_layout.setSpacing(20)

        # 最后更新时间
        update_container = QWidget()
        update_layout = QHBoxLayout(update_container)
        update_layout.setContentsMargins(0, 0, 0, 0)
        update_layout.setSpacing(8)

        update_icon = QLabel("🕐")
        update_icon.setStyleSheet("font-size: 14px;")
        update_layout.addWidget(update_icon)

        self.last_update_label = QLabel("最后更新: 未更新")
        self.last_update_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-weight: 500;
                font-size: 12px;
            }
        """)
        update_layout.addWidget(self.last_update_label)
        status_layout.addWidget(update_container)

        # 分隔符
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setStyleSheet("""
            QFrame {
                color: #e1e8ed;
                background-color: #e1e8ed;
                max-width: 1px;
            }
        """)
        status_layout.addWidget(separator1)

        # 下次更新时间
        next_container = QWidget()
        next_layout = QHBoxLayout(next_container)
        next_layout.setContentsMargins(0, 0, 0, 0)
        next_layout.setSpacing(8)

        next_icon = QLabel("⏰")
        next_icon.setStyleSheet("font-size: 14px;")
        next_layout.addWidget(next_icon)

        self.next_update_label = QLabel("下次更新: 5分钟后")
        self.next_update_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-weight: 500;
                font-size: 12px;
            }
        """)
        next_layout.addWidget(self.next_update_label)
        status_layout.addWidget(next_container)

        # 添加弹性空间
        status_layout.addStretch()

        # 网络状态
        self.network_status = QLabel("🌐 网络正常")
        self.network_status.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e8, stop:1 #c8e6c9);
                color: #2e7d32;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
            }
        """)
        status_layout.addWidget(self.network_status)

        # 分隔符
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setStyleSheet("""
            QFrame {
                color: #e1e8ed;
                background-color: #e1e8ed;
                max-width: 1px;
            }
        """)
        status_layout.addWidget(separator2)

        # 进度条容器
        progress_container = QWidget()
        progress_layout = QHBoxLayout(progress_container)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setSpacing(8)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(150)
        self.progress_bar.setMaximumHeight(16)
        self.progress_bar.setFormat("%p%")  # 显示百分比
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 8px;
                background-color: #f1f3f4;
                text-align: center;
                color: #2c3e50;
                font-weight: 600;
                font-size: 10px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4285f4, stop:1 #34a853);
                border-radius: 8px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)

        # 进度文本标签
        self.progress_label = QLabel("")
        self.progress_label.setVisible(False)
        self.progress_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-weight: 500;
                font-size: 11px;
                min-width: 60px;
            }
        """)
        progress_layout.addWidget(self.progress_label)

        status_layout.addWidget(progress_container)

        self.status_bar.addWidget(status_widget, 1)
    
    def setup_timer(self):
        """设置定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.auto_refresh)
        self.update_timer.start(UPDATE_INTERVAL)
        
        # 倒计时定时器
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_timer.start(1000)  # 每秒更新
        
        self.countdown_seconds = UPDATE_INTERVAL // 1000
    
    def connect_signals(self):
        """连接信号"""
        # 数据库信号
        self.db_manager.data_saved.connect(self.on_data_saved)
        self.db_manager.error_occurred.connect(self.on_error_occurred)
        
        # 表格信号
        self.popularity_table.stock_selected.connect(self.on_stock_selected)
        self.soaring_table.stock_selected.connect(self.on_stock_selected)
    
    def load_cached_data(self):
        """加载缓存数据并立即获取最新数据"""
        try:
            # 先加载缓存数据作为初始显示
            popularity_data = self.db_manager.get_latest_popularity_data()
            print(f"加载缓存人气榜数据: {len(popularity_data)}条")
            if popularity_data:
                self.popularity_table.update_data(popularity_data)
                # 更新统计信息
                rise_count = sum(1 for stock in popularity_data if stock.get('change_pct', 0) > 0)
                fall_count = sum(1 for stock in popularity_data if stock.get('change_pct', 0) < 0)
                self.popularity_stats.setText(f"📊 共{len(popularity_data)}只股票 | 📈 上涨{rise_count}只 | 📉 下跌{fall_count}只")

            soaring_data = self.db_manager.get_latest_soaring_data()
            print(f"加载缓存飙升榜数据: {len(soaring_data)}条")
            if soaring_data:
                self.soaring_table.update_data(soaring_data)
                # 更新统计信息
                rise_count = sum(1 for stock in soaring_data if stock.get('change_pct', 0) > 0)
                fall_count = sum(1 for stock in soaring_data if stock.get('change_pct', 0) < 0)
                self.soaring_stats.setText(f"📊 共{len(soaring_data)}只股票 | 📈 上涨{rise_count}只 | 📉 下跌{fall_count}只")

            # 更新总体统计
            self.update_overall_stats()

            # 更新最后更新时间
            last_update = self.db_manager.get_last_update_time()
            if last_update:
                self.last_update_label.setText(f"🕐 最后更新: {last_update}")

            # 立即获取最新实时数据
            print("立即获取最新实时数据...")
            self.manual_refresh()

        except Exception as e:
            print(f"加载缓存数据失败: {e}")
            # 如果加载失败，尝试获取新数据
            self.manual_refresh()
    
    def manual_refresh(self):
        """手动刷新"""
        if self.update_thread and self.update_thread.isRunning():
            QMessageBox.information(self, "提示", "数据更新中，请稍候...")
            return
        
        self.start_data_update()
    
    def auto_refresh(self):
        """自动刷新"""
        if not self.update_thread or not self.update_thread.isRunning():
            self.start_data_update()
        
        # 重置倒计时
        self.countdown_seconds = UPDATE_INTERVAL // 1000
    
    def start_data_update(self):
        """开始数据更新"""
        self.refresh_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        
        # 设置进度条范围（0-100%）
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_label.setText("准备更新...")
        
        # 创建更新线程
        self.update_thread = DataUpdateThread(self.scraper)
        self.update_thread.finished.connect(self.on_update_finished)
        
        # 连接爬虫信号到主窗口
        self.scraper.data_updated.connect(self.on_data_updated)
        self.scraper.error_occurred.connect(self.on_error_occurred)
        self.scraper.status_changed.connect(self.on_status_changed)
        
        # 启动进度更新定时器
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_timer.start(100)  # 每100ms更新一次进度
        
        self.update_thread.start()
    
    def on_update_finished(self):
        """更新完成"""
        self.refresh_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        
        # 停止进度更新定时器
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
        
        # 更新最后更新时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.last_update_label.setText(f"最后更新: {current_time}")
    
    def update_progress(self):
        """更新进度条"""
        if hasattr(self, 'progress_bar') and self.progress_bar.isVisible():
            current_value = self.progress_bar.value()
            
            # 模拟进度增长（实际项目中可以根据真实进度调整）
            if current_value < 90:  # 最多到90%，留10%给完成阶段
                new_value = min(current_value + 2, 90)
                self.progress_bar.setValue(new_value)
                
                # 更新进度文本
                if new_value < 30:
                    self.progress_label.setText("连接服务器...")
                elif new_value < 60:
                    self.progress_label.setText("获取数据...")
                elif new_value < 90:
                    self.progress_label.setText("处理数据...")
                else:
                    self.progress_label.setText("完成中...")
            elif current_value < 100:
                # 最后阶段
                self.progress_bar.setValue(100)
                self.progress_label.setText("更新完成!")
    
    def update_countdown(self):
        """更新倒计时"""
        if self.countdown_seconds > 0:
            minutes = self.countdown_seconds // 60
            seconds = self.countdown_seconds % 60
            self.next_update_label.setText(f"下次更新: {minutes:02d}:{seconds:02d}")
            self.countdown_seconds -= 1
        else:
            self.next_update_label.setText("下次更新: 即将更新...")
    
    def on_data_updated(self, data_type, data):
        """数据更新事件"""
        if data_type == 'popularity':
            self.popularity_table.update_data(data)
            self.db_manager.save_popularity_data(data)
            # 更新统计信息
            rise_count = sum(1 for stock in data if stock.get('change_pct', 0) > 0)
            fall_count = sum(1 for stock in data if stock.get('change_pct', 0) < 0)
            self.popularity_stats.setText(f"📊 共{len(data)}只股票 | 📈 上涨{rise_count}只 | 📉 下跌{fall_count}只")

        elif data_type == 'soaring':
            self.soaring_table.update_data(data)
            self.db_manager.save_soaring_data(data)
            # 更新统计信息
            rise_count = sum(1 for stock in data if stock.get('change_pct', 0) > 0)
            fall_count = sum(1 for stock in data if stock.get('change_pct', 0) < 0)
            self.soaring_stats.setText(f"📊 共{len(data)}只股票 | 📈 上涨{rise_count}只 | 📉 下跌{fall_count}只")

        # 更新总体统计
        self.update_overall_stats()
    
    def on_error_occurred(self, error_msg):
        """错误事件"""
        self.status_label.setText(f"错误: {error_msg}")
        self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
    
    def on_status_changed(self, status):
        """状态变化事件"""
        if "成功" in status:
            self.status_label.setText(f"🟢 {status}")
            self.status_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #e8f5e8, stop:1 #c8e6c9);
                    color: #2e7d32;
                    font-weight: 600;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                }
            """)
        elif "失败" in status or "错误" in status:
            self.status_label.setText(f"🔴 {status}")
            self.status_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffebee, stop:1 #ffcdd2);
                    color: #c62828;
                    font-weight: 600;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                }
            """)
        else:
            self.status_label.setText(f"🟡 {status}")
            self.status_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #fff3e0, stop:1 #ffe0b2);
                    color: #ef6c00;
                    font-weight: 600;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                }
            """)
    
    def on_data_saved(self, data_type, count):
        """数据保存事件"""
        self.status_label.setText(f"已保存{data_type}数据: {count}条")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
    
    def on_stock_selected(self, stock):
        """股票选中事件"""
        msg = f"股票信息:\n"
        msg += f"代码: {stock.get('code', '')}\n"
        msg += f"名称: {stock.get('name', '')}\n"
        msg += f"价格: {stock.get('price', 0):.2f}\n"
        msg += f"涨跌幅: {stock.get('change_pct', 0):+.2f}%"
        
        QMessageBox.information(self, "股票详情", msg)
    
    def on_search_text_changed(self, text):
        """搜索文本变化"""
        self.popularity_table.search_stock(text)
        self.soaring_table.search_stock(text)
    
    def clear_search(self):
        """清除搜索"""
        self.search_input.clear()

    def update_overall_stats(self):
        """更新总体统计信息"""
        popularity_data = self.popularity_table.stock_data
        soaring_data = self.soaring_table.stock_data

        total_stocks = len(popularity_data) + len(soaring_data)
        if total_stocks > 0:
            self.stats_label.setText(f"📊 人气榜: {len(popularity_data)}只 | 热股榜: {len(soaring_data)}只 | 总计: {total_stocks}只")
        else:
            self.stats_label.setText("📊 数据统计: 等待加载...")
    
    def export_data(self):
        """导出数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出Excel文件", 
            f"东方财富榜单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel文件 (*.xlsx)"
        )
        
        if file_path:
            success = self.db_manager.export_to_excel(file_path, 'both')
            if success:
                QMessageBox.information(self, "成功", f"数据已导出到:\n{file_path}")
            else:
                QMessageBox.warning(self, "失败", "导出失败，请检查文件路径和权限")
    
    def open_settings(self):
        """打开设置"""
        QMessageBox.information(self, "设置", "设置功能开发中...")
    
    def toggle_compact_mode(self):
        """切换紧凑模式"""
        QMessageBox.information(self, "紧凑模式", "紧凑模式切换功能开发中...")

    def show_help(self):
        """显示帮助"""
        help_text = """
东方财富股票榜单监控 v1.0

功能说明:
• 实时监控股票涨跌幅榜单(基于东方财富数据)
• 左表：按人气值排序的股票列表
• 右表：按涨跌幅排序的热门股票
• 自动每5分钟更新一次数据
• 支持手动刷新和数据导出
• 支持股票搜索和详情查看

数据说明:
• 数据来源：东方财富股票API
• 更新频率：实时更新
• 数据范围：A股市场股票

操作说明:
• 双击股票行查看详情
• 右键菜单可复制股票信息
• 搜索框支持代码和名称搜索
• 点击列标题可排序

快捷键:
• F5: 刷新数据
• Ctrl+E: 导出数据
• Ctrl+F: 搜索焦点
        """
        QMessageBox.information(self, "帮助", help_text)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.update_thread and self.update_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认退出", 
                "数据更新中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.update_thread.terminate()
                self.update_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
