"""
东方财富话题榜监控 - 两列布局版本
"""
import sys
import os
import json
import subprocess
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QStatusBar, 
                             QProgressBar, QMessageBox, QSplitter, QFrame,
                             QGroupBox, QAbstractScrollArea)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor

class TopicScraperThread(QThread):
    """话题榜爬取线程"""
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    progress = pyqtSignal(int, str)
    
    def run(self):
        """运行爬取任务"""
        try:
            self.progress.emit(10, "启动爬虫...")
            
            # 运行话题榜爬取脚本
            script_path = os.path.join(os.path.dirname(__file__), 'topic_scraper.py')
            if os.path.exists(script_path):
                self.progress.emit(20, "执行爬取脚本...")
                result = subprocess.run(['python', script_path], 
                                      capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    self.progress.emit(80, "读取数据...")
                    
                    # 读取生成的JSON文件
                    json_path = os.path.join(os.path.dirname(__file__), 'topic_stocks.json')
                    if os.path.exists(json_path):
                        with open(json_path, 'r', encoding='utf-8') as f:
                            stocks = json.load(f)
                        
                        self.progress.emit(100, "完成!")
                        self.finished.emit(stocks)
                    else:
                        self.error.emit("未找到数据文件")
                else:
                    self.error.emit(f"爬取脚本执行失败: {result.stderr}")
            else:
                self.error.emit("未找到爬取脚本")
                
        except Exception as e:
            self.error.emit(f"爬取失败: {str(e)}")

class TopicTableWidget(QTableWidget):
    """话题表格组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_table()
    
    def setup_table(self):
        """设置表格"""
        # 设置列数和标题
        self.setColumnCount(4)
        headers = ['排名', '话题标题', '阅读量', '讨论数']
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                selection-background-color: #e8f0fe;
                alternate-background-color: #fafbfc;
            }
            QTableWidget::item {
                padding: 8px 4px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f0fe, stop:1 #d2e3fc);
                color: #1a73e8;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #5f6368;
                padding: 10px 4px;
                border: none;
                border-bottom: 2px solid #4285f4;
                font-weight: 600;
                font-size: 12px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f0fe, stop:1 #d2e3fc);
            }
        """)
        
        # 设置列宽
        self.setColumnWidth(0, 60)   # 排名
        self.setColumnWidth(1, 400)  # 话题标题（增加宽度）
        self.setColumnWidth(2, 80)   # 阅读量
        self.setColumnWidth(3, 80)   # 讨论数
        
        # 设置表头
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 话题标题列自适应
        
        # 设置行高
        self.verticalHeader().setDefaultSectionSize(40)
        self.verticalHeader().setVisible(False)
        
        # 启用滚动条
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 设置表格大小策略
        self.setSizeAdjustPolicy(QAbstractScrollArea.AdjustToContents)
    
    def update_topics(self, stocks_data):
        """更新话题数据"""
        try:
            # 直接使用话题数据，每个话题包含related_stocks
            print(f"设置表格行数: {len(stocks_data)}")
            self.setRowCount(len(stocks_data))

            for row, topic in enumerate(stocks_data):
                # 排名
                rank_item = QTableWidgetItem(str(topic['rank']))
                rank_item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, 0, rank_item)

                # 话题标题
                title_item = QTableWidgetItem(topic['topic_title'])
                self.setItem(row, 1, title_item)

                # 阅读量
                view_item = QTableWidgetItem(topic['view_count'])
                view_item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, 2, view_item)

                # 讨论数
                comment_item = QTableWidgetItem(topic['comment_count'])
                comment_item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, 3, comment_item)

        except Exception as e:
            print(f"更新话题表格数据时出错: {e}")
            raise e

class StockTableWidget(QTableWidget):
    """股票表格组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_table()
    
    def setup_table(self):
        """设置表格"""
        # 设置列数和标题
        self.setColumnCount(5)
        headers = ['排名', '股票名称', '涨跌幅', '所属话题', '阅读量']
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                selection-background-color: #e8f0fe;
                alternate-background-color: #fafbfc;
            }
            QTableWidget::item {
                padding: 8px 4px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f0fe, stop:1 #d2e3fc);
                color: #1a73e8;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #5f6368;
                padding: 10px 4px;
                border: none;
                border-bottom: 2px solid #4285f4;
                font-weight: 600;
                font-size: 12px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f0fe, stop:1 #d2e3fc);
            }
        """)
        
        # 设置列宽
        self.setColumnWidth(0, 60)   # 排名
        self.setColumnWidth(1, 120)  # 股票名称
        self.setColumnWidth(2, 80)   # 涨跌幅
        self.setColumnWidth(3, 200)  # 所属话题
        self.setColumnWidth(4, 80)   # 阅读量
        
        # 设置表头
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 所属话题列自适应
        
        # 设置行高
        self.verticalHeader().setDefaultSectionSize(40)
        self.verticalHeader().setVisible(False)
    
    def update_stocks(self, stocks):
        """更新股票数据"""
        try:
            self.setRowCount(len(stocks))

            for row, stock in enumerate(stocks):
                # 排名（基于涨跌幅排序）
                rank_item = QTableWidgetItem(str(row + 1))
                rank_item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, 0, rank_item)

                # 股票名称
                name_item = QTableWidgetItem(stock['stock_name'])
                self.setItem(row, 1, name_item)

                # 涨跌幅
                change_pct = stock['change_pct']
                change_pct_item = QTableWidgetItem(f"{change_pct:+.2f}%")
                change_pct_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

                # 设置颜色
                if change_pct > 0:
                    change_pct_item.setForeground(QColor('#e53e3e'))  # 红色
                elif change_pct < 0:
                    change_pct_item.setForeground(QColor('#38a169'))  # 绿色
                else:
                    change_pct_item.setForeground(QColor('#718096'))  # 灰色

                self.setItem(row, 2, change_pct_item)

                # 所属话题
                topic_text = f"话题{stock['topic_rank']}: {stock['topic_title'][:30]}..."
                topic_item = QTableWidgetItem(topic_text)
                self.setItem(row, 3, topic_item)

                # 阅读量
                view_item = QTableWidgetItem(stock['view_count'])
                view_item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, 4, view_item)

        except Exception as e:
            print(f"更新股票表格数据时出错: {e}")
            raise e

class TopicMainWindow(QMainWindow):
    """话题榜主窗口"""
    
    def __init__(self):
        super().__init__()
        self.scraper_thread = None
        self.current_stocks = []  # 当前显示的股票数据
        self.setup_ui()
        self.setup_timer()
        # 启动时自动加载数据
        self.refresh_data()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("东方财富话题榜监控")
        self.setGeometry(100, 100, 1400, 800)
        self.setMinimumSize(1000, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(2)
        main_layout.setContentsMargins(2, 2, 2, 2)
        
        # 创建顶部栏
        top_bar_widget = self.create_top_bar()
        main_layout.addWidget(top_bar_widget)
        
        # 创建表格区域
        tables_widget = self.create_tables()
        main_layout.addWidget(tables_widget, 1)  # 设置拉伸因子为1，占用剩余空间
        
        # 创建状态栏
        self.create_status_bar()
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QWidget {
                font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial;
            }
        """)
    
    def create_top_bar(self):
        """创建顶部栏"""
        # 创建顶部栏容器
        top_bar_widget = QWidget()
        top_bar_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 6px;
                padding: 2px;
            }
        """)
        top_bar_widget.setMaximumHeight(50)

        layout = QHBoxLayout(top_bar_widget)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 5, 8, 5)

        # 标题
        title_label = QLabel("🔥 东方财富话题榜")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 700;
                color: #2c3e50;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 统计信息
        self.stats_label = QLabel("数据统计: 等待加载...")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-size: 12px;
                font-weight: 500;
                background: #f1f3f4;
                padding: 6px 12px;
                border-radius: 12px;
            }
        """)
        layout.addWidget(self.stats_label)
        
        # 自动更新开关按钮
        self.auto_refresh_btn = QPushButton("⏰ 自动更新")
        self.auto_refresh_btn.setCheckable(True)
        self.auto_refresh_btn.setChecked(True)  # 默认开启
        self.auto_refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34a853, stop:1 #2e7d32);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 11px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #46b566, stop:1 #34a853);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2e7d32, stop:1 #1b5e20);
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34a853, stop:1 #2e7d32);
            }
            QPushButton:!checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc3545, stop:1 #c62828);
            }
        """)
        self.auto_refresh_btn.clicked.connect(self.toggle_auto_refresh)
        layout.addWidget(self.auto_refresh_btn)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4285f4, stop:1 #3367d6);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 11px;
                min-width: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a95f5, stop:1 #4285f4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3367d6, stop:1 #2c5aa0);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(self.refresh_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(150)
        self.progress_bar.setFormat("%p%")
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 6px;
                background-color: #f1f3f4;
                text-align: center;
                color: #2c3e50;
                font-weight: 600;
                font-size: 10px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b6b, stop:1 #ee5a24);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        return top_bar_widget
    
    def create_tables(self):
        """创建表格区域"""
        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)
        
        # 左侧话题表格
        left_group = QGroupBox("🔥 热门话题")
        left_group.setStyleSheet("""
            QGroupBox {
                background: white;
                border: none;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 12px;
                font-weight: 700;
                font-size: 14px;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)
        
        left_layout = QVBoxLayout(left_group)
        left_layout.setContentsMargins(8, 8, 8, 8)
        
        self.topic_table = TopicTableWidget()
        left_layout.addWidget(self.topic_table)
        
        self.splitter.addWidget(left_group)
        
        # 右侧股票表格
        right_group = QGroupBox("📈 相关股票")
        right_group.setStyleSheet("""
            QGroupBox {
                background: white;
                border: none;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 12px;
                font-weight: 700;
                font-size: 14px;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }
        """)
        
        right_layout = QVBoxLayout(right_group)
        right_layout.setContentsMargins(8, 8, 8, 8)
        
        self.stock_table = StockTableWidget()
        right_layout.addWidget(self.stock_table)
        
        self.splitter.addWidget(right_group)
        
        # 设置分割器比例
        self.splitter.setSizes([700, 700])
        
        return self.splitter
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-top: 1px solid #e1e8ed;
                color: #5f6368;
                font-size: 12px;
                padding: 8px;
            }
        """)
        self.setStatusBar(self.status_bar)
        
        # 状态信息
        self.status_label = QLabel("🟢 系统就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e8, stop:1 #c8e6c9);
                color: #2e7d32;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
            }
        """)
        self.status_bar.addWidget(self.status_label)
        
        # 添加弹性空间
        self.status_bar.addPermanentWidget(QLabel(""))
        
        # 更新时间
        self.update_time_label = QLabel("最后更新: 未更新")
        self.update_time_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-weight: 500;
                font-size: 12px;
            }
        """)
        self.status_bar.addPermanentWidget(self.update_time_label)
    
    def setup_timer(self):
        """设置定时器"""
        # 自动刷新间隔（分钟）
        self.auto_refresh_interval = 5  # 5分钟自动更新
        
        # 自动刷新定时器
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.auto_refresh_data)
        self.auto_refresh_timer.start(self.auto_refresh_interval * 60 * 1000)  # 转换为毫秒
        
        # 倒计时定时器（每秒更新）
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_timer.start(1000)  # 1秒
        
        # 初始化倒计时
        self.countdown_seconds = self.auto_refresh_interval * 60
    
    def refresh_data(self):
        """刷新数据"""
        if self.scraper_thread and self.scraper_thread.isRunning():
            QMessageBox.information(self, "提示", "数据更新中，请稍候...")
            return
        
        # 禁用刷新按钮
        self.refresh_btn.setEnabled(False)
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 更新状态
        self.status_label.setText("🟡 正在更新数据...")
        self.status_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff3e0, stop:1 #ffe0b2);
                color: #ef6c00;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
            }
        """)
        
        # 创建并启动爬取线程
        self.scraper_thread = TopicScraperThread()
        self.scraper_thread.finished.connect(self.on_data_ready)
        self.scraper_thread.error.connect(self.on_error)
        self.scraper_thread.progress.connect(self.on_progress)
        self.scraper_thread.start()
    
    def auto_refresh_data(self):
        """自动刷新数据"""
        # 只有在自动更新开启时才执行
        if self.auto_refresh_btn.isChecked():
            if not self.scraper_thread or not self.scraper_thread.isRunning():
                print("自动刷新数据...")
                self.refresh_data()
            
            # 重置倒计时
            self.countdown_seconds = self.auto_refresh_interval * 60
    
    def toggle_auto_refresh(self):
        """切换自动更新开关"""
        if self.auto_refresh_btn.isChecked():
            # 开启自动更新
            self.auto_refresh_timer.start(self.auto_refresh_interval * 60 * 1000)
            self.countdown_timer.start(1000)
            self.countdown_seconds = self.auto_refresh_interval * 60
            self.auto_refresh_btn.setText("⏰ 自动更新")
            print("自动更新已开启")
        else:
            # 关闭自动更新
            self.auto_refresh_timer.stop()
            self.countdown_timer.stop()
            self.auto_refresh_btn.setText("⏸️ 已暂停")
            self.update_time_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')} | 自动更新已暂停")
            print("自动更新已暂停")
    
    def update_countdown(self):
        """更新倒计时显示"""
        # 只有在自动更新开启时才显示倒计时
        if self.auto_refresh_btn.isChecked():
            if self.countdown_seconds > 0:
                minutes = self.countdown_seconds // 60
                seconds = self.countdown_seconds % 60
                self.update_time_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')} | 下次更新: {minutes:02d}:{seconds:02d}")
                self.countdown_seconds -= 1
            else:
                self.update_time_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')} | 下次更新: 即将更新...")
    
    def on_progress(self, value, text):
        """进度更新"""
        self.progress_bar.setValue(value)
    
    def on_data_ready(self, stocks):
        """数据准备完成"""
        try:
            # 保存当前数据
            self.current_stocks = stocks

            # 更新话题表格
            print(f"准备更新话题表格，数据条数: {len(stocks)}")
            self.topic_table.update_topics(stocks)

            # 提取所有相关股票用于股票表格显示，并去重
            stock_dict = {}  # 用于去重，key为股票名称
            for topic in stocks:
                if 'related_stocks' in topic and topic['related_stocks']:
                    for stock in topic['related_stocks']:
                        stock_name = stock['name']
                        # 如果股票不存在或者当前股票涨跌幅更高，则更新
                        if stock_name not in stock_dict or stock['change_pct'] > stock_dict[stock_name]['change_pct']:
                            stock_dict[stock_name] = {
                                'stock_code': f"00000{len(stock_dict)+1}",  # 生成代码
                                'stock_name': stock_name,
                                'change_pct': stock['change_pct'],
                                'view_count': topic['view_count'],  # 使用话题的阅读量
                                'comment_count': topic['comment_count'],  # 使用话题的讨论数
                                'topic_rank': topic['rank'],
                                'topic_title': topic['topic_title']
                            }

            # 转换为列表并按涨跌幅排序
            all_stocks = list(stock_dict.values())
            all_stocks.sort(key=lambda x: x['change_pct'], reverse=True)

            # 更新股票表格（显示所有相关股票）
            self.stock_table.update_stocks(all_stocks)

            # 更新统计信息
            total_topics = len(stocks)
            total_stocks = len(all_stocks)
            rise_count = sum(1 for stock in all_stocks if stock.get('change_pct', 0) > 0)
            fall_count = sum(1 for stock in all_stocks if stock.get('change_pct', 0) < 0)
            self.stats_label.setText(f"📊 {total_topics}个话题 | {total_stocks}只股票 | 📈 上涨{rise_count}只 | 📉 下跌{fall_count}只")

        except Exception as e:
            print(f"更新数据时出错: {e}")
            QMessageBox.warning(self, "错误", f"更新数据时出错:\n{str(e)}")

        # 更新状态
        self.status_label.setText("🟢 数据更新成功")
        self.status_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e8, stop:1 #c8e6c9);
                color: #2e7d32;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
            }
        """)

        # 更新最后更新时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.update_time_label.setText(f"最后更新: {current_time}")

        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用刷新按钮
        self.refresh_btn.setEnabled(True)
    
    def on_error(self, error_msg):
        """错误处理"""
        # 更新状态
        self.status_label.setText("🔴 更新失败")
        self.status_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffebee, stop:1 #ffcdd2);
                color: #c62828;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
            }
        """)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 启用刷新按钮
        self.refresh_btn.setEnabled(True)
        
        # 显示错误消息
        QMessageBox.warning(self, "错误", f"数据更新失败:\n{error_msg}")

def main():
    """主函数"""
    # 设置高DPI支持 - 必须在创建QApplication之前设置
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("东方财富话题榜监控")
    app.setApplicationVersion("1.0.0")
    
    # 设置紧凑字体
    font = QFont("Microsoft YaHei", 8)
    app.setFont(font)
    
    # 设置现代化样式
    app.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
        }

        QWidget {
            font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial;
        }

        /* 紧凑工具栏按钮样式 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4285f4, stop:1 #3367d6);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 11px;
            min-width: 60px;
        }
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #5a95f5, stop:1 #4285f4);
        }
        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3367d6, stop:1 #2c5aa0);
        }
        QPushButton:disabled {
            background: #bdc3c7;
            color: #7f8c8d;
        }

        /* 紧凑搜索框样式 */
        QLineEdit {
            background-color: white;
            border: 1px solid #e1e8ed;
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 12px;
            color: #2c3e50;
        }
        QLineEdit:focus {
            border-color: #4285f4;
            outline: none;
        }
        QLineEdit::placeholder {
            color: #95a5a6;
        }

        /* 紧凑分组框样式 */
        QGroupBox {
            background: white;
            border: none;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 12px;
            font-weight: 700;
            font-size: 14px;
            color: #2c3e50;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 8px 0 8px;
        }
    """)
    
    # 创建主窗口
    window = TopicMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()